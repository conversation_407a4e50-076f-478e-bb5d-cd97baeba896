# Google Sheets API Configuration
# 方法1: Service Account (推薦用於生產環境)
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
GOOGLE_PROJECT_ID=your-google-project-id

# 方法2: OAuth2 (適用於開發環境)
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret

# Google Sheets Configuration
GOOGLE_SPREADSHEET_ID=your-spreadsheet-id-here

# Sheet Names (工作表名稱)
PARTICIPANTS_SHEET_NAME=參加者資料
ACTIVITIES_SHEET_NAME=活動資料
ATTENDANCE_SHEET_NAME=出席記錄
SESSIONS_SHEET_NAME=屆別資料

# Sync Configuration
AUTO_SYNC_ENABLED=true
SYNC_INTERVAL_MINUTES=5
OFFLINE_MODE=false

# Development
NODE_ENV=development
