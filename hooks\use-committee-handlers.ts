"use client"

import { useCallback } from "react"
import type { Activity } from "../types"

interface UseCommitteeHandlersProps {
  committees: string[]
  activities: Activity[]
  setCommittees: (committees: string[]) => void
  setActivities: (activities: Activity[]) => void
}

export function useCommitteeHandlers(props: UseCommitteeHandlersProps) {
  const { committees, activities, setCommittees, setActivities } = props

  const handleAddCommittee = useCallback(
    (committee: string) => {
      setCommittees([...committees, committee])
    },
    [committees, setCommittees],
  )

  const handleBulkAddCommittees = useCallback(
    (newCommittees: string[]) => {
      setCommittees([...committees, ...newCommittees])
    },
    [committees, setCommittees],
  )

  const handleEditCommittee = useCallback(
    (oldName: string, newName: string) => {
      setCommittees(committees.map((c) => (c === oldName ? newName : c)))
      setActivities(
        activities.map((activity) => ({
          ...activity,
          committee: activity.committee === oldName ? newName : activity.committee,
        })),
      )
    },
    [committees, activities, setCommittees, setActivities],
  )

  const handleDeleteCommittee = useCallback(
    (committee: string) => {
      setCommittees(committees.filter((c) => c !== committee))
      setActivities(
        activities.map((activity) => ({
          ...activity,
          committee: activity.committee === committee ? "其他委員會" : activity.committee,
        })),
      )
    },
    [committees, activities, setCommittees, setActivities],
  )

  return {
    handleAddCommittee,
    handleBulkAddCommittees,
    handleEditCommittee,
    handleDeleteCommittee,
  }
}
