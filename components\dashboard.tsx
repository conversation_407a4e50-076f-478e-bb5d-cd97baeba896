"use client"

import { useMemo, useState } from "react"
import { cn } from "@/lib/utils"
import type { Activity, Participant, Session, ActivityLevelSettings } from "../types"
import { calculateStatistics } from "../utils/statistics"
import { ExportDialog } from "./export-dialog"
import { ActivityLevelSettingsComponent } from "./activity-level-settings"
import {
  getActivityLevel,
  getActivityLevelText,
  getActivityLevelColor,
  DEFAULT_ACTIVITY_LEVEL_SETTINGS,
} from "../utils/activity-level"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
} from "recharts"

interface DashboardProps {
  activities: Activity[]
  allParticipants: Participant[]
  sessions?: Session[]
  onViewActivities: () => void
  onViewStatistics: () => void
  onAddActivity: () => void
  activityLevelSettings?: ActivityLevelSettings
  onUpdateActivityLevelSettings?: (settings: ActivityLevelSettings) => void
}

export function Dashboard({
  activities,
  allParticipants,
  sessions = [],
  onViewActivities,
  onViewStatistics,
  onAddActivity,
  activityLevelSettings: propActivityLevelSettings,
  onUpdateActivityLevelSettings: propOnUpdateActivityLevelSettings,
}: DashboardProps) {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showActivityLevelSettings, setShowActivityLevelSettings] = useState(false)
  const [activityLevelSettings, setActivityLevelSettings] = useState<ActivityLevelSettings>(
    propActivityLevelSettings || DEFAULT_ACTIVITY_LEVEL_SETTINGS,
  )

  // 當 props 中的設置改變時，更新本地狀態
  const handleActivityLevelSettingsChange = (settings: ActivityLevelSettings) => {
    setActivityLevelSettings(settings)
    propOnUpdateActivityLevelSettings?.(settings)
  }
  const [selectedTimeRange, setSelectedTimeRange] = useState<"week" | "month" | "quarter" | "all">("month")

  // 計算統計數據
  const statistics = useMemo(() => calculateStatistics(activities, allParticipants), [activities, allParticipants])

  // 計算趨勢數據
  const trendData = useMemo(() => {
    const sortedActivities = [...activities].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    return sortedActivities.map((activity, index) => {
      const attendedCount = activity.participants.filter(p => p.attendance[activity.date]).length
      const attendanceRate = activity.participants.length > 0 ? (attendedCount / activity.participants.length) * 100 : 0

      // 計算累積平均出席率
      const previousActivities = sortedActivities.slice(0, index + 1)
      const totalAttended = previousActivities.reduce((sum, act) => {
        return sum + act.participants.filter(p => p.attendance[act.date]).length
      }, 0)
      const totalPossible = previousActivities.reduce((sum, act) => sum + act.participants.length, 0)
      const cumulativeRate = totalPossible > 0 ? (totalAttended / totalPossible) * 100 : 0

      return {
        date: activity.date,
        name: activity.name.length > 10 ? activity.name.substring(0, 10) + '...' : activity.name,
        attendanceRate: Math.round(attendanceRate * 10) / 10,
        cumulativeRate: Math.round(cumulativeRate * 10) / 10,
        participantCount: activity.participants.length,
        committee: activity.committee
      }
    })
  }, [activities])

  // 智能洞察和建議
  const insights = useMemo(() => {
    const insights: Array<{type: 'success' | 'warning' | 'danger' | 'info', title: string, description: string, action?: string}> = []

    // 出席率趨勢分析
    if (trendData.length >= 3) {
      const recentRates = trendData.slice(-3).map(d => d.attendanceRate)
      const isDecreasing = recentRates.every((rate, i) => i === 0 || rate < recentRates[i - 1])
      const isIncreasing = recentRates.every((rate, i) => i === 0 || rate > recentRates[i - 1])

      if (isDecreasing) {
        insights.push({
          type: 'warning',
          title: '出席率下降趨勢',
          description: '最近3次活動的出席率持續下降，需要關注參與度問題',
          action: '建議檢視活動安排和參與者反饋'
        })
      } else if (isIncreasing) {
        insights.push({
          type: 'success',
          title: '出席率上升趨勢',
          description: '最近3次活動的出席率持續上升，參與度良好',
        })
      }
    }

    // 委員會活動平衡分析
    const committeeCount = activities.reduce((acc, activity) => {
      acc[activity.committee] = (acc[activity.committee] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const maxCount = Math.max(...Object.values(committeeCount))
    const minCount = Math.min(...Object.values(committeeCount))

    if (maxCount > minCount * 2) {
      insights.push({
        type: 'info',
        title: '委員會活動分布不均',
        description: '某些委員會的活動數量明顯較多，建議平衡各委員會的活動安排',
        action: '考慮增加活動較少委員會的活動'
      })
    }

    // 參與者風險預警
    const riskParticipants = statistics.participantStats.filter(p =>
      p.attendanceRate < 40 && p.totalActivities >= 3
    ).length

    if (riskParticipants > 0) {
      insights.push({
        type: 'danger',
        title: `${riskParticipants}位參與者需要關注`,
        description: '這些參與者的出席率低於40%，可能面臨流失風險',
        action: '建議主動聯繫並了解原因'
      })
    }

    // 活動頻率建議
    if (activities.length >= 2) {
      const dates = activities.map(a => new Date(a.date)).sort((a, b) => a.getTime() - b.getTime())
      const intervals = []
      for (let i = 1; i < dates.length; i++) {
        intervals.push((dates[i].getTime() - dates[i-1].getTime()) / (1000 * 60 * 60 * 24))
      }
      const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length

      if (avgInterval > 14) {
        insights.push({
          type: 'info',
          title: '活動頻率較低',
          description: `平均活動間隔為${Math.round(avgInterval)}天，可考慮增加活動頻率`,
          action: '建議每週或雙週舉辦活動'
        })
      }
    }

    return insights
  }, [activities, statistics, trendData])

  // KPI 指標
  const kpis = useMemo(() => {
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()

    const thisMonthActivities = activities.filter(a => {
      const date = new Date(a.date)
      return date.getMonth() === currentMonth && date.getFullYear() === currentYear
    })

    const lastMonthActivities = activities.filter(a => {
      const date = new Date(a.date)
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear
      return date.getMonth() === lastMonth && date.getFullYear() === lastMonthYear
    })

    const thisMonthAvgAttendance = thisMonthActivities.length > 0 ?
      thisMonthActivities.reduce((sum, activity) => {
        const attended = activity.participants.filter(p => p.attendance[activity.date]).length
        return sum + (activity.participants.length > 0 ? (attended / activity.participants.length) * 100 : 0)
      }, 0) / thisMonthActivities.length : 0

    const lastMonthAvgAttendance = lastMonthActivities.length > 0 ?
      lastMonthActivities.reduce((sum, activity) => {
        const attended = activity.participants.filter(p => p.attendance[activity.date]).length
        return sum + (activity.participants.length > 0 ? (attended / activity.participants.length) * 100 : 0)
      }, 0) / lastMonthActivities.length : 0

    const attendanceChange = lastMonthAvgAttendance > 0 ?
      ((thisMonthAvgAttendance - lastMonthAvgAttendance) / lastMonthAvgAttendance) * 100 : 0

    const activeParticipants = statistics.participantStats.filter(p => p.totalActivities > 0).length
    const engagementRate = allParticipants.length > 0 ? (activeParticipants / allParticipants.length) * 100 : 0

    return {
      thisMonthActivities: thisMonthActivities.length,
      lastMonthActivities: lastMonthActivities.length,
      thisMonthAvgAttendance: Math.round(thisMonthAvgAttendance * 10) / 10,
      attendanceChange: Math.round(attendanceChange * 10) / 10,
      engagementRate: Math.round(engagementRate * 10) / 10,
      activeParticipants
    }
  }, [activities, statistics, allParticipants])

  // 委員會表現分析
  const committeePerformance = useMemo(() => {
    const performance = activities.reduce((acc, activity) => {
      if (!acc[activity.committee]) {
        acc[activity.committee] = {
          name: activity.committee,
          totalActivities: 0,
          totalParticipants: 0,
          totalAttended: 0,
          avgAttendanceRate: 0
        }
      }

      const attended = activity.participants.filter(p => p.attendance[activity.date]).length
      acc[activity.committee].totalActivities++
      acc[activity.committee].totalParticipants += activity.participants.length
      acc[activity.committee].totalAttended += attended

      return acc
    }, {} as Record<string, any>)

    return Object.values(performance).map((committee: any) => ({
      ...committee,
      avgAttendanceRate: committee.totalParticipants > 0 ?
        Math.round((committee.totalAttended / committee.totalParticipants) * 1000) / 10 : 0
    })).sort((a, b) => b.avgAttendanceRate - a.avgAttendanceRate)
  }, [activities])

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d"]

  return (
    <div className="space-y-6">
      {/* 頂部 KPI 卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow p-4 text-white">
          <h3 className="text-sm font-medium opacity-90">本月活動數</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.thisMonthActivities}</p>
            <div className="text-right">
              <p className="text-xs opacity-75">上月: {kpis.lastMonthActivities}</p>
              <p className={cn(
                "text-xs font-medium",
                kpis.thisMonthActivities >= kpis.lastMonthActivities ? "text-green-200" : "text-red-200"
              )}>
                {kpis.thisMonthActivities >= kpis.lastMonthActivities ? "↗" : "↘"}
                {Math.abs(kpis.thisMonthActivities - kpis.lastMonthActivities)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow p-4 text-white">
          <h3 className="text-sm font-medium opacity-90">本月平均出席率</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.thisMonthAvgAttendance}%</p>
            <div className="text-right">
              <p className={cn(
                "text-xs font-medium",
                kpis.attendanceChange >= 0 ? "text-green-200" : "text-red-200"
              )}>
                {kpis.attendanceChange >= 0 ? "↗" : "↘"} {Math.abs(kpis.attendanceChange).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow p-4 text-white">
          <h3 className="text-sm font-medium opacity-90">參與度</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.engagementRate}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75">{kpis.activeParticipants}/{allParticipants.length}</p>
              <p className="text-xs font-medium text-purple-200">活躍參與者</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow p-4 text-white">
          <h3 className="text-sm font-medium opacity-90">整體出席率</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{statistics.averageAttendanceRate.toFixed(1)}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75">目標: 80%</p>
              <p className={cn(
                "text-xs font-medium",
                statistics.averageAttendanceRate >= 80 ? "text-green-200" : "text-yellow-200"
              )}>
                {statistics.averageAttendanceRate >= 80 ? "達標" : "待改善"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 智能洞察 */}
      {insights.length > 0 && (
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            智能洞察
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {insights.map((insight, index) => (
              <div key={index} className={cn(
                "p-3 rounded-lg border-l-4",
                insight.type === 'success' && "bg-green-50 dark:bg-green-900/20 border-green-500",
                insight.type === 'warning' && "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500",
                insight.type === 'danger' && "bg-red-50 dark:bg-red-900/20 border-red-500",
                insight.type === 'info' && "bg-blue-50 dark:bg-blue-900/20 border-blue-500"
              )}>
                <h4 className={cn(
                  "font-medium text-sm",
                  insight.type === 'success' && "text-green-800 dark:text-green-200",
                  insight.type === 'warning' && "text-yellow-800 dark:text-yellow-200",
                  insight.type === 'danger' && "text-red-800 dark:text-red-200",
                  insight.type === 'info' && "text-blue-800 dark:text-blue-200"
                )}>
                  {insight.title}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">{insight.description}</p>
                {insight.action && (
                  <p className="text-xs font-medium mt-2 opacity-75">
                    💡 {insight.action}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 趨勢分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">出席率趨勢</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis domain={[0, 100]} />
                <Tooltip
                  formatter={(value, name) => [
                    `${value}%`,
                    name === 'attendanceRate' ? '當次出席率' : '累積平均出席率'
                  ]}
                  labelFormatter={(label) => `日期: ${label}`}
                />
                <Area
                  type="monotone"
                  dataKey="cumulativeRate"
                  stackId="1"
                  stroke="#8884d8"
                  fill="#8884d8"
                  fillOpacity={0.3}
                />
                <Line
                  type="monotone"
                  dataKey="attendanceRate"
                  stroke="#82ca9d"
                  strokeWidth={2}
                  dot={{ fill: '#82ca9d', strokeWidth: 2, r: 4 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">委員會表現排行</h3>
          <div className="space-y-3">
            {committeePerformance.map((committee, index) => (
              <div key={committee.name} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded">
                <div className="flex items-center">
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3",
                    index === 0 && "bg-yellow-500",
                    index === 1 && "bg-gray-400",
                    index === 2 && "bg-orange-500",
                    index > 2 && "bg-blue-500"
                  )}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{committee.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {committee.totalActivities} 個活動
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={cn(
                    "text-lg font-bold",
                    committee.avgAttendanceRate >= 80 ? "text-green-600 dark:text-green-400" :
                    committee.avgAttendanceRate >= 60 ? "text-yellow-600 dark:text-yellow-400" :
                    "text-red-600 dark:text-red-400"
                  )}>
                    {committee.avgAttendanceRate}%
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {committee.totalAttended}/{committee.totalParticipants}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={onAddActivity}
            className="flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            新增活動
          </button>
          <button
            onClick={onViewStatistics}
            className="flex items-center justify-center p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            詳細統計
          </button>
          <button
            onClick={() => setShowExportDialog(true)}
            className="flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            導出數據
          </button>
        </div>
      </div>

      {/* 導出對話框 */}
      {showExportDialog && (
        <ExportDialog
          activities={activities}
          allParticipants={allParticipants}
          onClose={() => setShowExportDialog(false)}
        />
      )}
    </div>
  )
}
