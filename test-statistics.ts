import { generateTestDataSet, calculateTestDataStats } from './data/test-data-generator'
import { calculateStatistics } from './utils/statistics'

// 生成測試數據並驗證統計功能
const runStatisticsTest = () => {
  console.log('🚀 開始生成測試數據...')
  
  // 生成500位參加者和50個活動的測試數據
  const testData = generateTestDataSet(500, 50)
  const { participants, activities } = testData
  
  console.log(`✅ 測試數據生成完成:`)
  console.log(`   - 參加者數量: ${participants.length}`)
  console.log(`   - 活動數量: ${activities.length}`)
  
  // 使用測試數據生成器計算基礎統計
  const basicStats = calculateTestDataStats(participants, activities)
  console.log('\n📊 基礎統計數據:')
  console.log(`   - 總可能報名人次: ${basicStats.totalPossibleRegistrations}`)
  console.log(`   - 實際報名人次: ${basicStats.totalRegistrations}`)
  console.log(`   - 實際出席人次: ${basicStats.totalAttendances}`)
  console.log(`   - 爽約人次: ${basicStats.totalNoShows}`)
  console.log(`   - 報名率: ${basicStats.registrationRate}%`)
  console.log(`   - 出席率: ${basicStats.attendanceRate}%`)
  console.log(`   - 爽約率: ${basicStats.noShowRate}%`)
  
  // 使用系統統計函數計算詳細統計
  const systemStats = calculateStatistics(activities, participants)
  console.log('\n🔍 系統統計驗證:')
  console.log(`   - 總活動數: ${systemStats.totalActivities}`)
  console.log(`   - 總參加者數: ${systemStats.totalParticipants}`)
  console.log(`   - 平均出席率: ${systemStats.averageAttendanceRate.toFixed(2)}%`)
  console.log(`   - 總出席記錄: ${systemStats.totalAttendanceRecords}`)
  console.log(`   - 總可能出席: ${systemStats.totalPossibleAttendance}`)
  console.log(`   - 缺席人次: ${systemStats.totalPossibleAttendance - systemStats.totalAttendanceRecords}`)
  
  // 分析參加者統計
  console.log('\n👥 參加者統計分析:')
  const participantStats = systemStats.participantStats
  
  // 按類別分組統計
  const categoryStats: Record<string, { count: number; avgAttendance: number; avgRegistration: number }> = {}
  participantStats.forEach(stat => {
    const category = participants.find(p => p.id === stat.id)?.category || '未知'
    if (!categoryStats[category]) {
      categoryStats[category] = { count: 0, avgAttendance: 0, avgRegistration: 0 }
    }
    categoryStats[category].count++
    categoryStats[category].avgAttendance += stat.attendanceRate
  })
  
  Object.keys(categoryStats).forEach(category => {
    const stats = categoryStats[category]
    stats.avgAttendance = stats.avgAttendance / stats.count
    
    // 計算該類別的平均報名率
    const categoryParticipants = participants.filter(p => p.category === category)
    let totalRegistrations = 0
    let totalPossible = 0
    
    categoryParticipants.forEach(participant => {
      activities.forEach(activity => {
        totalPossible++
        if (participant.registration[activity.date]) {
          totalRegistrations++
        }
      })
    })
    
    stats.avgRegistration = totalPossible > 0 ? (totalRegistrations / totalPossible) * 100 : 0
    
    console.log(`   ${category}:`)
    console.log(`     - 人數: ${stats.count}`)
    console.log(`     - 平均報名率: ${stats.avgRegistration.toFixed(2)}%`)
    console.log(`     - 平均出席率: ${stats.avgAttendance.toFixed(2)}%`)
  })
  
  // 高缺席率參加者分析
  const highAbsenceParticipants = participantStats
    .filter(p => p.attendanceRate < 60)
    .sort((a, b) => a.attendanceRate - b.attendanceRate)
    .slice(0, 10)
  
  console.log('\n⚠️  高缺席率參加者 (前10名):')
  highAbsenceParticipants.forEach((stat, index) => {
    const participant = participants.find(p => p.id === stat.id)
    console.log(`   ${index + 1}. ${participant?.name} (${participant?.category}) - 出席率: ${stat.attendanceRate.toFixed(2)}%`)
  })
  
  // 月度趨勢分析
  console.log('\n📈 月度趨勢分析:')
  systemStats.monthlyTrends.forEach(trend => {
    console.log(`   ${trend.month}: 出席率 ${trend.rate.toFixed(2)}% (${trend.activities} 個活動)`)
  })
  
  // 驗證數據一致性
  console.log('\n✅ 數據一致性驗證:')
  const isConsistent = Math.abs(basicStats.attendanceRate - systemStats.averageAttendanceRate) < 1
  console.log(`   基礎統計出席率: ${basicStats.attendanceRate}%`)
  console.log(`   系統統計出席率: ${systemStats.averageAttendanceRate.toFixed(2)}%`)
  console.log(`   數據一致性: ${isConsistent ? '✅ 通過' : '❌ 不一致'}`)
  
  return {
    basicStats,
    systemStats,
    testData,
    isConsistent
  }
}

// 執行測試
if (typeof window === 'undefined') {
  // Node.js 環境下直接執行
  runStatisticsTest()
}

export { runStatisticsTest }
export default runStatisticsTest