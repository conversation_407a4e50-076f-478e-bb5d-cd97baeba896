import { cn } from "@/lib/utils"
import type { Participant, SessionParticipant, Session } from "../../types"
import { ParticipantTable } from "./participant-table"
import { AddExistingParticipantDialog } from "./add-existing-participant-dialog"

interface ParticipantManagementContentProps {
  // 數據
  currentSession: Session | null
  currentSessionParticipants: SessionParticipant[]
  currentSessionCategories: string[]
  allParticipants: Participant[]
  allCategories: string[]
  filteredSessionParticipants: SessionParticipant[]
  availableGlobalParticipants: Participant[]

  // 狀態
  viewMode: "session" | "global"
  searchTerm: string
  categoryFilter: string
  statusFilter: "all" | "active" | "inactive"
  showAddExisting: boolean
  sortField: string
  sortDirection: "asc" | "desc"

  // 事件處理
  onViewModeChange: (mode: "session" | "global") => void
  onSearchTermChange: (term: string) => void
  onCategoryFilterChange: (category: string) => void
  onStatusFilterChange: (status: "all" | "active" | "inactive") => void
  onShowAddExistingChange: (show: boolean) => void
  onSort: (field: string) => void
  onEditSessionParticipant: (participant: SessionParticipant) => void
  onRemoveFromSession: (sessionParticipantId: string) => void
  onViewHistory: (participant: SessionParticipant) => void
  onBatchAddToSession: (selectedParticipants: Set<string>) => void
  onDeleteParticipant: (participantId: string) => void
  onEditParticipant: (participant: Participant) => void
}

/**
 * 參加者管理內容區域組件
 * 用途：管理主要內容區域的顯示和交互
 */
export function ParticipantManagementContent({
  currentSession,
  currentSessionParticipants,
  currentSessionCategories,
  allParticipants,
  allCategories,
  filteredSessionParticipants,
  availableGlobalParticipants,
  viewMode,
  searchTerm,
  categoryFilter,
  statusFilter,
  showAddExisting,
  sortField,
  sortDirection,
  onViewModeChange,
  onSearchTermChange,
  onCategoryFilterChange,
  onStatusFilterChange,
  onShowAddExistingChange,
  onSort,
  onEditSessionParticipant,
  onRemoveFromSession,
  onViewHistory,
  onBatchAddToSession,
  onDeleteParticipant,
  onEditParticipant,
}: ParticipantManagementContentProps) {
  return (
    <>
      {/* 當前屆別信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">當前屆別</h3>
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
              {currentSession?.name || "未選擇屆別"}
            </span>
            {currentSession && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {currentSession.startDate} - {currentSession.endDate}
              </span>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            屆別參加者: {currentSessionParticipants.length} 人
            <br />
            所有成員: {allParticipants.length} 人
          </div>
        </div>
      </div>

      {/* 視圖模式切換 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => onViewModeChange("session")}
              className={cn(
                "px-4 py-2 rounded-md transition-colors",
                viewMode === "session"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
              )}
            >
              屆別參加者
            </button>
            <button
              onClick={() => onViewModeChange("global")}
              className={cn(
                "px-4 py-2 rounded-md transition-colors",
                viewMode === "global"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
              )}
            >
              所有成員
            </button>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {viewMode === "session"
              ? `顯示 ${filteredSessionParticipants.length} / ${currentSessionParticipants.length} 位屆別參加者`
              : `顯示 ${allParticipants.length} 位所有成員`}
          </div>
        </div>
      </div>

      {viewMode === "session" ? (
        <>
          {/* 搜索和過濾 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索參加者</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => onSearchTermChange(e.target.value)}
                placeholder="輸入參加者姓名"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜過濾</label>
              <select
                value={categoryFilter}
                onChange={(e) => onCategoryFilterChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部職銜</option>
                {currentSessionCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">狀態過濾</label>
              <select
                value={statusFilter}
                onChange={(e) => onStatusFilterChange(e.target.value as "all" | "active" | "inactive")}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="all">全部狀態</option>
                <option value="active">活躍</option>
                <option value="inactive">非活躍</option>
              </select>
            </div>
          </div>

          {/* 屆別參加者列表 */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {currentSession?.name} 參加者列表 ({filteredSessionParticipants.length})
              </h3>
            </div>
            <ParticipantTable
              participants={filteredSessionParticipants}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={onSort}
              onEdit={onEditSessionParticipant}
              onRemove={onRemoveFromSession}
              onViewHistory={onViewHistory}
            />
          </div>

          {/* 添加現有參加者功能 */}
          {availableGlobalParticipants.length > 0 && (
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  添加現有參加者到 {currentSession?.name}
                </h3>
                <button
                  onClick={() => onShowAddExistingChange(!showAddExisting)}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  {showAddExisting ? "收起" : `添加成員 (${availableGlobalParticipants.length})`}
                </button>
              </div>
            </div>
          )}

          {/* 添加現有參加者對話框 */}
          {showAddExisting && (
            <AddExistingParticipantDialog
              availableParticipants={availableGlobalParticipants}
              allCategories={allCategories}
              onAdd={onBatchAddToSession}
              onClose={() => onShowAddExistingChange(false)}
            />
          )}
        </>
      ) : (
        /* 全局參加者視圖 */
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              所有成員列表 ({allParticipants.length})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    姓名
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    職銜
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {allParticipants.map((participant) => (
                  <tr key={participant.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.category || "未設定"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => onEditParticipant(participant)}
                        className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                      >
                        編輯
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`確定要刪除 "${participant.name}" 嗎？這將同時移除其所有屆別記錄。`)) {
                            onDeleteParticipant(participant.id)
                          }
                        }}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        刪除
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </>
  )
}
