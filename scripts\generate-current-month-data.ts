import { generateCurrentMonthTestData } from '../data/test-data-generator'
import { writeFileSync } from 'fs'
import { join } from 'path'

// 生成當月測試數據
const generateAndSaveCurrentMonthData = () => {
  console.log('開始生成當月測試數據...')
  
  // 生成100個活動和200位參與者的當月數據
  const testData = generateCurrentMonthTestData(100, 200)
  
  // 保存參與者數據
  const participantsPath = join(__dirname, '../data/current-month-participants.json')
  writeFileSync(participantsPath, JSON.stringify(testData.participants, null, 2), 'utf-8')
  console.log(`參與者數據已保存到: ${participantsPath}`)
  
  // 保存活動數據
  const activitiesPath = join(__dirname, '../data/current-month-activities.json')
  writeFileSync(activitiesPath, JSON.stringify(testData.activities, null, 2), 'utf-8')
  console.log(`活動數據已保存到: ${activitiesPath}`)
  
  // 保存統計摘要
  const statsPath = join(__dirname, '../data/current-month-stats.json')
  const summary = {
    generatedAt: new Date().toISOString(),
    dateRange: {
      start: testData.activityDates[0],
      end: testData.activityDates[testData.activityDates.length - 1]
    },
    ...testData.stats
  }
  writeFileSync(statsPath, JSON.stringify(summary, null, 2), 'utf-8')
  console.log(`統計摘要已保存到: ${statsPath}`)
  
  console.log('\n=== 當月測試數據生成完成 ===')
  console.log(`活動數量: ${testData.stats.totalActivities}`)
  console.log(`參與者數量: ${testData.stats.totalParticipants}`)
  console.log(`報名率: ${testData.stats.registrationRate}%`)
  console.log(`出席率: ${testData.stats.attendanceRate}%`)
  console.log(`日期範圍: ${testData.activityDates[0]} 至 ${testData.activityDates[testData.activityDates.length - 1]}`)
  
  return testData
}

// 如果直接運行此腳本
if (require.main === module) {
  generateAndSaveCurrentMonthData()
}

export { generateAndSaveCurrentMonthData }