"use client"

import { cn } from "@/lib/utils"
import type { SessionParticipant } from "../../types"
import { getActivityLevelText, getActivityLevelColor } from "../../utils/activity-level"

interface ParticipantWithStats extends SessionParticipant {
  attendanceRate: number
  activityLevel: string
  totalParticipated: number
  totalAttended: number
}

interface ParticipantTableProps {
  participants: ParticipantWithStats[]
  sortField: string
  sortDirection: "asc" | "desc"
  onSort: (field: string) => void
  onEdit: (participant: SessionParticipant) => void
  onRemove: (participantId: string) => void
  onViewHistory: (participant: any) => void
}

export function ParticipantTable({
  participants,
  sortField,
  sortDirection,
  onSort,
  onEdit,
  onRemove,
  onViewHistory,
}: ParticipantTableProps) {
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      )
    }
    return sortDirection === "asc" ? (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    )
  }

  return (
    <div className="card-professional overflow-hidden animate-fadeIn">
      <div className="overflow-x-auto">
        <table className="table-professional">
          <thead>
            <tr>
              <th
                className="cursor-pointer hover:bg-base-200/50 transition-all duration-200 font-semibold"
                onClick={() => onSort("name")}
              >
                <div className="flex items-center gap-2 font-medium">
                  姓名
                  {getSortIcon("name")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200/50 transition-all duration-200 font-semibold"
                onClick={() => onSort("category")}
              >
                <div className="flex items-center gap-2 font-medium">
                  職銜
                  {getSortIcon("category")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200/50 transition-all duration-200 font-semibold"
                onClick={() => onSort("joinDate")}
              >
                <div className="flex items-center gap-2 font-medium">
                  加入日期
                  {getSortIcon("joinDate")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200/50 transition-all duration-200 font-semibold"
                onClick={() => onSort("status")}
              >
                <div className="flex items-center gap-2 font-medium">
                  狀態
                  {getSortIcon("status")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200/50 transition-all duration-200 font-semibold"
                onClick={() => onSort("attendanceRate")}
              >
                <div className="flex items-center gap-2 font-medium">
                  出席率
                  {getSortIcon("attendanceRate")}
                </div>
              </th>
              <th className="font-semibold">活躍等級</th>
              <th className="font-semibold">操作</th>
            </tr>
          </thead>
          <tbody>
            {participants.length > 0 ? (
              participants.map((participant) => (
                <tr key={participant.id} className="hover:bg-base-200">
                  <td>{participant.name}</td>
                  <td>{participant.category || "未設定"}</td>
                  <td>{participant.joinDate || "未設定"}</td>
                  <td>
                    <div
                      className={cn(
                        "status-indicator",
                        participant.isActive ? "status-success" : "status-error"
                      )}
                    >
                      {participant.isActive ? "✓ 活躍" : "⚠ 非活躍"}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-3">
                      <span className="font-medium text-sm min-w-[3rem]">{participant.attendanceRate.toFixed(1)}%</span>
                      <div className="flex-1">
                        <progress
                          className={cn(
                            "progress-professional w-20 h-2",
                            participant.attendanceRate >= 80
                              ? "progress-success"
                              : participant.attendanceRate >= 60
                                ? "progress-warning"
                                : "progress-error"
                          )}
                          value={participant.attendanceRate}
                          max="100"
                        />
                      </div>
                    </div>
                    <div className="text-xs text-base-content/60 mt-1 font-medium">
                      {participant.totalAttended}/{participant.totalParticipated} 次參與
                    </div>
                  </td>
                  <td>
                    <div
                      className={cn(
                        "status-indicator",
                        getActivityLevelColor(participant.activityLevel) === "badge-success" ? "status-success" :
                        getActivityLevelColor(participant.activityLevel) === "badge-info" ? "status-info" :
                        getActivityLevelColor(participant.activityLevel) === "badge-error" ? "status-error" : "status-warning"
                      )}
                    >
                      {getActivityLevelText(participant.activityLevel)}
                    </div>
                  </td>
                  <td>
                    <div className="flex gap-1">
                      <button
                        onClick={() => onViewHistory(participant)}
                        className="btn-professional btn-ghost btn-xs tooltip tooltip-top"
                        data-tip="查看出席歷史"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onEdit(participant)}
                        className="btn-professional btn-ghost btn-xs tooltip tooltip-top"
                        data-tip="編輯參加者"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`確定要從當前屆別移除 "${participant.name}" 嗎？`)) {
                            onRemove(participant.id)
                          }
                        }}
                        className="btn-professional btn-ghost btn-xs text-error hover:bg-error/10 tooltip tooltip-top"
                        data-tip="移除參加者"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center py-8 text-base-content opacity-60">
                  沒有找到符合條件的參加者
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
