import Papa from "papaparse"
import * as XLSX from "xlsx"
import type { Participant } from "../types"

// 用於解析CSV文件
export function parseCSV(file: File): Promise<Omit<Participant, "id" | "attendance">[]> {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          const participants = results.data
            .filter((row: any) => {
              // 過濾掉空行或無效行
              const name = row.name || row["姓名"] || row["名稱"] || ""
              return name.trim() !== ""
            })
            .map((row: any) => {
              const name = row.name || row["姓名"] || row["名稱"] || ""
              const category = row.category || row["類別"] || row["類型"] || row["组別"] || ""
              
              return {
                name: name.trim(),
                category: category.trim(),
              }
            })
          
          if (participants.length === 0) {
            throw new Error("CSV文件中未找到有效的參加者數據，請確保文件包含姓名欄位（name、姓名或名稱）")
          }
          
          resolve(participants)
        } catch (error) {
          reject(error)
        }
      },
      error: (error) => {
        reject(error)
      },
    })
  })
}

// 用於解析Excel文件
export function parseExcel(file: File): Promise<Omit<Participant, "id" | "attendance">[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = e.target?.result
        if (!data) {
          throw new Error("文件讀取失敗")
        }

        const workbook = XLSX.read(data, { type: "binary" })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 將Excel轉換為JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        const participants = jsonData
          .filter((row: any) => {
            // 過濾掉空行或無效行
            const name = row.name || row["姓名"] || row["名稱"] || ""
            return name && name.toString().trim() !== ""
          })
          .map((row: any) => {
            const name = row.name || row["姓名"] || row["名稱"] || ""
            const category = row.category || row["類別"] || row["類型"] || row["组別"] || ""
            
            return {
              name: name.toString().trim(),
              category: category ? category.toString().trim() : "",
            }
          })
        
        if (participants.length === 0) {
          throw new Error("Excel文件中未找到有效的參加者數據，請確保文件包含姓名欄位（name、姓名或名稱）")
        }
        resolve(participants)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = (error) => {
      reject(error)
    }

    reader.readAsBinaryString(file)
  })
}

// 檢測文件類型並選擇適當的解析器
export async function parseFile(file: File): Promise<Omit<Participant, "id" | "attendance">[]> {
  const fileExt = file.name.split(".").pop()?.toLowerCase()

  if (fileExt === "csv") {
    return parseCSV(file)
  } else if (["xlsx", "xls"].includes(fileExt || "")) {
    return parseExcel(file)
  } else {
    throw new Error("不支持的文件格式，請上傳CSV或Excel(xlsx/xls)文件")
  }
}
