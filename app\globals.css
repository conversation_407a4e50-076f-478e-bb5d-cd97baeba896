@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    scroll-behavior: smooth;
  }

  body {
    @apply antialiased;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Professional Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 focus:ring-offset-2 focus:ring-offset-base-100;
  }

  /* Elegant Shadows */
  .shadow-elegant {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-elegant-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-elegant-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Enhanced Animations */
  .animate-fadeIn {
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slideUp {
    animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-scaleIn {
    animation: scaleIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(16px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

/* Professional Component Enhancements */
@layer components {
  /* Enhanced Cards */
  .card-professional {
    @apply card bg-base-100 shadow-elegant border border-base-300 border-opacity-50 transition-all duration-200;
  }

  .card-professional:hover {
    @apply shadow-elegant-lg transform -translate-y-0.5;
  }

  /* Premium Buttons */
  .btn-professional {
    @apply btn font-medium tracking-wide transition-all duration-200 focus-ring;
  }

  .btn-professional:hover {
    @apply transform -translate-y-0.5 shadow-elegant;
  }

  /* Elegant Form Controls */
  .input-professional {
    @apply input bg-base-100 border border-base-300 focus:border-primary transition-all duration-200 focus-ring;
  }

  .select-professional {
    @apply select bg-base-100 border border-base-300 focus:border-primary transition-all duration-200 focus-ring;
  }

  .textarea-professional {
    @apply textarea bg-base-100 border border-base-300 focus:border-primary transition-all duration-200 focus-ring;
  }

  /* Professional Tables */
  .table-professional {
    @apply table table-zebra;
  }

  .table-professional thead th {
    @apply bg-base-200 bg-opacity-50 font-semibold text-base-content text-opacity-80 border-base-300;
  }

  .table-professional tbody tr:hover {
    @apply bg-base-200 bg-opacity-30 transition-colors duration-150;
  }

  /* Enhanced Modals */
  .modal-professional .modal-box {
    @apply shadow-elegant-xl border border-base-300 border-opacity-50 animate-scaleIn;
  }

  /* Professional Navigation */
  .nav-professional {
    @apply bg-base-100 bg-opacity-80 backdrop-blur-sm border-b border-base-300 border-opacity-50 shadow-elegant;
  }

  /* Status Indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply status-indicator bg-success bg-opacity-10 text-success border border-success border-opacity-20;
  }

  .status-warning {
    @apply status-indicator bg-warning bg-opacity-10 text-warning border border-warning border-opacity-20;
  }

  .status-error {
    @apply status-indicator bg-error bg-opacity-10 text-error border border-error border-opacity-20;
  }

  .status-info {
    @apply status-indicator bg-info bg-opacity-10 text-info border border-info border-opacity-20;
  }

  /* Professional Progress Bars */
  .progress-professional {
    @apply progress bg-base-300 bg-opacity-50;
  }

  /* Elegant Dividers */
  .divider-professional {
    @apply divider text-base-content text-opacity-60 font-medium;
  }
}
