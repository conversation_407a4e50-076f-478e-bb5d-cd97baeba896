"use client"

import { useCallback } from "react"
import type { Activity, Session, SessionParticipant } from "../types"

interface UseSessionHandlersProps {
  sessions: Session[]
  activities: Activity[]
  sessionParticipants: SessionParticipant[]
  selectedSessionId: string | null
  setSessions: (sessions: Session[]) => void
  setActivities: (activities: Activity[]) => void
  setSessionParticipants: (sessionParticipants: SessionParticipant[]) => void
  setSelectedSessionId: (sessionId: string | null) => void
}

export function useSessionHandlers(props: UseSessionHandlersProps) {
  const {
    sessions,
    activities,
    sessionParticipants,
    selectedSessionId,
    setSessions,
    setActivities,
    setSessionParticipants,
    setSelectedSessionId,
  } = props

  const handleAddSession = useCallback(
    (newSession: Omit<Session, "id">) => {
      const id = (sessions.length + 1).toString()
      const updatedSessions = newSession.isActive ? sessions.map((s) => ({ ...s, isActive: false })) : sessions

      setSessions([...updatedSessions, { id, ...newSession }])

      if (newSession.isActive) {
        setSelectedSessionId(id)
      }
    },
    [sessions, setSessions, setSelectedSessionId],
  )

  const handleEditSession = useCallback(
    (updatedSession: Session) => {
      const updatedSessions = updatedSession.isActive
        ? sessions.map((s) => (s.id === updatedSession.id ? updatedSession : { ...s, isActive: false }))
        : sessions.map((s) => (s.id === updatedSession.id ? updatedSession : s))

      setSessions(updatedSessions)

      if (updatedSession.isActive) {
        setSelectedSessionId(updatedSession.id)
      }
    },
    [sessions, setSessions, setSelectedSessionId],
  )

  const handleDeleteSession = useCallback(
    (sessionId: string) => {
      setSessions(sessions.filter((s) => s.id !== sessionId))
      setActivities(activities.filter((a) => a.sessionId !== sessionId))
      setSessionParticipants(sessionParticipants.filter((sp) => sp.sessionId !== sessionId))

      if (selectedSessionId === sessionId) {
        const remainingSessions = sessions.filter((s) => s.id !== sessionId)
        setSelectedSessionId(remainingSessions.find((s) => s.isActive)?.id || remainingSessions[0]?.id || null)
      }
    },
    [
      sessions,
      activities,
      sessionParticipants,
      selectedSessionId,
      setSessions,
      setActivities,
      setSessionParticipants,
      setSelectedSessionId,
    ],
  )

  const handleSetActiveSession = useCallback(
    (sessionId: string) => {
      setSessions(sessions.map((s) => ({ ...s, isActive: s.id === sessionId })))
      setSelectedSessionId(sessionId)
    },
    [sessions, setSessions, setSelectedSessionId],
  )

  return {
    handleAddSession,
    handleEditSession,
    handleDeleteSession,
    handleSetActiveSession,
  }
}
