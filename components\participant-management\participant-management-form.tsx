import type { Participant, SessionParticipant } from "../../types"

interface ParticipantManagementFormProps {
  // 新增參加者狀態
  newParticipant: { name: string; category: string }
  newParticipantTitle: string
  isAddingParticipant: boolean
  allCategories: string[]

  // 編輯狀態
  editingParticipant: Participant | null
  editingSessionParticipant: SessionParticipant | null

  // 事件處理
  onNewParticipantChange: (participant: { name: string; category: string }) => void
  onNewParticipantTitleChange: (title: string) => void
  onAddParticipant: () => void
  onEditingParticipantChange: (participant: Participant | null) => void
  onEditingSessionParticipantChange: (participant: SessionParticipant | null) => void
  onUpdateParticipant: () => void
  onUpdateSessionParticipant: () => void
}

/**
 * 參加者管理表單組件
 * 用途：處理新增和編輯參加者的表單
 */
export function ParticipantManagementForm({
  newParticipant,
  newParticipantTitle,
  isAddingParticipant,
  allCategories,
  editingParticipant,
  editingSessionParticipant,
  onNewParticipantChange,
  onNewParticipantTitleChange,
  onAddParticipant,
  onEditingParticipantChange,
  onEditingSessionParticipantChange,
  onUpdateParticipant,
  onUpdateSessionParticipant,
}: ParticipantManagementFormProps) {
  return (
    <>
      {/* 新增參加者表單 */}
      <div className="card-professional animate-fadeIn">
        <div className="card-body p-6">
          <h3 className="card-title text-lg font-semibold mb-6 flex items-center gap-2">
            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            新增參加者
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium text-base-content/80">姓名</span>
              </label>
              <input
                type="text"
                value={newParticipant.name}
                onChange={(e) => onNewParticipantChange({ ...newParticipant, name: e.target.value })}
                placeholder="輸入參加者姓名"
                className="input-professional"
              />
            </div>
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium text-base-content/80">職銜</span>
              </label>
              <select
                value={newParticipant.category}
                onChange={(e) => onNewParticipantChange({ ...newParticipant, category: e.target.value })}
                className="select-professional"
              >
                <option value="">選擇職銜</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
                <option value="新增職銜">+ 新增職銜</option>
              </select>
            </div>
            {newParticipant.category === "新增職銜" && (
              <div className="form-control animate-slideUp">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">新職銜名稱</span>
                </label>
                <input
                  type="text"
                  value={newParticipantTitle}
                  onChange={(e) => onNewParticipantTitleChange(e.target.value)}
                  placeholder="輸入新職銜名稱"
                  className="input-professional"
                />
              </div>
            )}
          </div>
          <div className="card-actions justify-start mt-6">
            <button
              onClick={onAddParticipant}
              disabled={!newParticipant.name.trim() || isAddingParticipant || (newParticipant.category === "新增職銜" && !newParticipantTitle.trim())}
              className="btn-professional btn-primary gap-2"
            >
              {isAddingParticipant ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  新增中...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  新增參加者
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 編輯參加者對話框 */}
      {editingParticipant && (
        <dialog className="modal modal-open modal-professional">
          <div className="modal-box">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                編輯參加者
              </h3>
              <button
                onClick={() => onEditingParticipantChange(null)}
                className="btn-professional btn-sm btn-circle btn-ghost"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-5">
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">姓名</span>
                </label>
                <input
                  type="text"
                  value={editingParticipant.name}
                  onChange={(e) => onEditingParticipantChange({ ...editingParticipant, name: e.target.value })}
                  className="input-professional"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">職銜</span>
                </label>
                <select
                  value={editingParticipant.category || ""}
                  onChange={(e) => onEditingParticipantChange({ ...editingParticipant, category: e.target.value })}
                  className="select-professional"
                >
                  <option value="">選擇職銜</option>
                  {allCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="modal-action mt-8">
              <button
                onClick={() => onEditingParticipantChange(null)}
                className="btn-professional btn-ghost"
              >
                取消
              </button>
              <button
                onClick={onUpdateParticipant}
                className="btn-professional btn-primary gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                更新
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => onEditingParticipantChange(null)}>close</button>
          </form>
        </dialog>
      )}

      {/* 編輯屆別參加者對話框 */}
      {editingSessionParticipant && (
        <dialog className="modal modal-open">
          <div className="modal-box">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">編輯屆別參加者</h3>
              <button
                onClick={() => onEditingSessionParticipantChange(null)}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">姓名</span>
                </label>
                <input
                  type="text"
                  value={editingSessionParticipant.name}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, name: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">職銜</span>
                </label>
                <input
                  type="text"
                  value={editingSessionParticipant.category || ""}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, category: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">加入日期</span>
                </label>
                <input
                  type="date"
                  value={editingSessionParticipant.joinDate}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, joinDate: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text">活躍狀態</span>
                  <input
                    type="checkbox"
                    checked={editingSessionParticipant.isActive}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, isActive: e.target.checked })}
                    className="toggle toggle-primary"
                  />
                </label>
              </div>
            </div>
            <div className="modal-action">
              <button
                onClick={() => onEditingSessionParticipantChange(null)}
                className="btn btn-ghost"
              >
                取消
              </button>
              <button
                onClick={onUpdateSessionParticipant}
                className="btn btn-primary"
              >
                更新
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => onEditingSessionParticipantChange(null)}>close</button>
          </form>
        </dialog>
      )}
    </>
  )
}
