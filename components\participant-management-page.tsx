"use client"

import { cn } from "@/lib/utils"
import type { Participant, SessionParticipant, Session, Activity, ActivityLevelSettings } from "../types"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS } from "../utils/activity-level"

// 導入子組件
import { ParticipantManagementHeader } from "./participant-management/participant-management-header"
import { ParticipantManagementStats } from "./participant-management/participant-management-stats"
import { ParticipantManagementDialogs } from "./participant-management/participant-management-dialogs"
import { ParticipantManagementContent } from "./participant-management/participant-management-content"
import { ParticipantManagementForm } from "./participant-management/participant-management-form"

// 導入自定義 hooks
import { useParticipantManagementState } from "../hooks/use-participant-management-state"
import { useParticipantManagementHandlers } from "../hooks/use-participant-management-handlers"
import { useParticipantManagement } from "../hooks/use-participant-management"

interface ParticipantManagementPageProps {
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  sessions: Session[]
  selectedSessionId: string | null
  onAddParticipant: (participant: Omit<Participant, "id" | "attendance">) => void
  onBulkAddSessionParticipants: (sessionParticipants: Omit<SessionParticipant, "id">[]) => void
  onBulkAddParticipants: (participants: Omit<Participant, "id" | "attendance">[]) => void
  onUpdateParticipant: (participant: Participant) => void
  onUpdateSessionParticipant: (sessionParticipant: SessionParticipant) => void
  onDeleteParticipant: (participantId: string) => void
  onRemoveFromSession: (sessionParticipantId: string) => void
  onBulkDeleteTitle: (title: string) => { participantsUpdated: number; placeholdersRemoved: number; sessionParticipantsUpdated: number }
  onBack: () => void
  activities: Activity[]
  activityLevelSettings?: ActivityLevelSettings
}

export function ParticipantManagementPage({
  allParticipants,
  sessionParticipants,
  sessions,
  selectedSessionId,
  onAddParticipant,
  onBulkAddSessionParticipants,
  onBulkAddParticipants,
  onUpdateParticipant,
  onUpdateSessionParticipant,
  onDeleteParticipant,
  onRemoveFromSession,
  onBulkDeleteTitle,
  onBack,
  activities,
  activityLevelSettings = DEFAULT_ACTIVITY_LEVEL_SETTINGS,
}: ParticipantManagementPageProps) {
  // 使用自定義狀態管理 Hook
  const state = useParticipantManagementState()

  // 使用自定義 Hook 處理參加者管理邏輯
  const {
    currentSession,
    currentSessionParticipants,
    currentSessionCategories,
    allCategories,
    statistics,
    filteredSessionParticipants,
    availableGlobalParticipants,
  } = useParticipantManagement({
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    activities,
    activityLevelSettings,
    searchTerm: state.searchTerm,
    categoryFilter: state.categoryFilter,
    statusFilter: state.statusFilter,
    sortField: state.sortField,
    sortDirection: state.sortDirection,
  })

  // 使用事件處理 Hook
  const handlers = useParticipantManagementHandlers({
    allParticipants,
    sessionParticipants,
    selectedSessionId,
    onAddParticipant,
    onBulkAddSessionParticipants,
    onBulkAddParticipants,
    onUpdateParticipant,
    onUpdateSessionParticipant,
    onBulkDeleteTitle,
    allCategories,
    ...state,
  })

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <ParticipantManagementHeader
        onBack={onBack}
        onShowTitleManagement={() => state.setShowTitleManagement(true)}
        onShowBulkImport={() => state.setShowBulkImport(true)}
      />

      {/* 統計信息 */}
      <ParticipantManagementStats
        activityLevelSettings={activityLevelSettings}
        statistics={statistics}
        selectedSessionId={selectedSessionId}
      />

      {/* 對話框 */}
      <ParticipantManagementDialogs
        showTitleManagement={state.showTitleManagement}
        showBulkImport={state.showBulkImport}
        viewingAttendanceHistory={state.viewingAttendanceHistory}
        allCategories={allCategories}
        sessions={sessions}
        selectedSessionId={selectedSessionId}
        activities={activities}
        sessionParticipants={sessionParticipants}
        onUpdateTitle={handlers.handleUpdateTitle}
        onDeleteTitle={handlers.handleDeleteTitle}
        onBulkImport={handlers.handleBulkImport}
        onCloseTitleManagement={() => state.setShowTitleManagement(false)}
        onCloseBulkImport={() => state.setShowBulkImport(false)}
        onCloseAttendanceHistory={() => state.setViewingAttendanceHistory(null)}
      />

      {/* 主要內容 */}
      <ParticipantManagementContent
        currentSession={currentSession}
        currentSessionParticipants={currentSessionParticipants}
        currentSessionCategories={currentSessionCategories}
        allParticipants={allParticipants}
        allCategories={allCategories}
        filteredSessionParticipants={filteredSessionParticipants}
        availableGlobalParticipants={availableGlobalParticipants}
        viewMode={state.viewMode}
        searchTerm={state.searchTerm}
        categoryFilter={state.categoryFilter}
        statusFilter={state.statusFilter}
        showAddExisting={state.showAddExisting}
        sortField={state.sortField}
        sortDirection={state.sortDirection}
        onViewModeChange={state.setViewMode}
        onSearchTermChange={state.setSearchTerm}
        onCategoryFilterChange={state.setCategoryFilter}
        onStatusFilterChange={state.setStatusFilter}
        onShowAddExistingChange={state.setShowAddExisting}
        onSort={handlers.handleSort}
        onEditSessionParticipant={state.setEditingSessionParticipant}
        onRemoveFromSession={onRemoveFromSession}
        onViewHistory={(participant) => {
          const globalParticipant = allParticipants.find(
            (p) => p.id === participant.participantId,
          )
          if (globalParticipant) {
            state.setViewingAttendanceHistory(globalParticipant)
          }
        }}
        onBatchAddToSession={handlers.handleBatchAddToSession}
        onDeleteParticipant={onDeleteParticipant}
        onEditParticipant={state.setEditingParticipant}
      />

      {/* 新增參加者表單 */}
      <ParticipantManagementForm
        newParticipant={state.newParticipant}
        newParticipantTitle={state.newParticipantTitle}
        isAddingParticipant={state.isAddingParticipant}
        allCategories={allCategories}
        editingParticipant={state.editingParticipant}
        editingSessionParticipant={state.editingSessionParticipant}
        onNewParticipantChange={state.setNewParticipant}
        onNewParticipantTitleChange={state.setNewParticipantTitle}
        onAddParticipant={handlers.handleAddParticipant}
        onEditingParticipantChange={state.setEditingParticipant}
        onEditingSessionParticipantChange={state.setEditingSessionParticipant}
        onUpdateParticipant={handlers.handleUpdateParticipant}
        onUpdateSessionParticipant={handlers.handleUpdateSessionParticipant}
      />
    </div>
  )
}
