"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import type { Activity, Session } from "../types"
import { ExportDialog } from "./export-dialog"
import { UniversalBulkImport } from "./universal-bulk-import"

interface ActivityManagementProps {
  activities: Activity[]
  committees: string[]
  sessions?: Session[]
  selectedSessionId?: string | null
  onAddActivity: (activity: Omit<Activity, "id" | "participants">) => void
  onBulkAddActivitiesWithParticipants: (
    data: {
      activity: Omit<Activity, "id" | "participants">
      participants: Omit<import("../types").Participant, "id" | "attendance">[]
    }[],
  ) => void // 添加這行
  onSelectActivity: (activityId: string) => void
  onDeleteActivity: (activityId: string) => void
  onEditActivity: (activity: Activity) => void
  onViewStatistics: () => void
  onManageCommittees: () => void
}

export function ActivityManagement({
  activities,
  committees,
  sessions = [],
  selectedSessionId,
  onAddActivity,
  onBulkAddActivitiesWithParticipants, // 添加這行
  onSelectActivity,
  onDeleteActivity,
  onEditActivity,
  onViewStatistics,
  onManageCommittees,
}: ActivityManagementProps) {
  const [newActivity, setNewActivity] = useState({
    date: "",
    name: "",
    committee: committees[0] || "",
    description: "",
  })
  const [editingActivity, setEditingActivity] = useState<Activity | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [committeeFilter, setCommitteeFilter] = useState("")
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showBulkImport, setShowBulkImport] = useState(false)
  const [sortField, setSortField] = useState<"date" | "name" | "committee" | "participants">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  // 處理新增活動
  const handleAddActivity = () => {
    if (newActivity.date && newActivity.name && newActivity.committee) {
      onAddActivity(newActivity)
      setNewActivity({
        date: "",
        name: "",
        committee: committees[0] || "",
        description: "",
      })
    }
  }

  // 處理更新活動
  const handleUpdateActivity = () => {
    if (editingActivity) {
      onEditActivity(editingActivity)
      setEditingActivity(null)
    }
  }

  // 處理批量導入
  const handleBulkImport = (importedData: any) => {
    onBulkAddActivitiesWithParticipants(importedData)
    setShowBulkImport(false)
  }

  // 處理排序
  const handleSort = (field: "date" | "name" | "committee" | "participants") => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortOrder("asc")
    }
  }

  // 獲取排序圖標
  const getSortIcon = (field: "date" | "name" | "committee" | "participants") => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      )
    }
    return sortOrder === "asc" ? (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    )
  }

  // 過濾活動
  const filteredActivities = activities.filter((activity) => {
    const matchesSearch =
      activity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.date.includes(searchTerm)

    const matchesCommittee = committeeFilter ? activity.committee === committeeFilter : true

    return matchesSearch && matchesCommittee
  })

  // 按選定字段和順序排序活動
  const sortedActivities = [...filteredActivities].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortField) {
      case "date":
        aValue = new Date(a.date).getTime()
        bValue = new Date(b.date).getTime()
        break
      case "name":
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case "committee":
        aValue = a.committee.toLowerCase()
        bValue = b.committee.toLowerCase()
        break
      case "participants":
        aValue = a.participants.length
        bValue = b.participants.length
        break
      default:
        aValue = new Date(a.date).getTime()
        bValue = new Date(b.date).getTime()
    }

    if (aValue < bValue) return sortOrder === "asc" ? -1 : 1
    if (aValue > bValue) return sortOrder === "asc" ? 1 : -1
    return 0
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">活動管理</h2>
        <div className="flex space-x-2">
          <button
            onClick={onManageCommittees}
            className="px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            委員會管理
          </button>
          <button
            onClick={onViewStatistics}
            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            查看統計
          </button>
          {/* 修改批量導入按鈕為下拉菜單 */}
          <button
            onClick={() => setShowBulkImport(true)}
            className="px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
              />
            </svg>
            批量導入活動和參加者
          </button>
          <button
            onClick={() => setShowExportDialog(true)}
            className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            導出數據
          </button>
        </div>
      </div>

      {/* 屆別選擇器 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">當前屆別:</label>
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
              {sessions.find((s) => s.id === selectedSessionId)?.name || "未選擇屆別"}
            </span>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">顯示 {activities.length} 個活動</div>
        </div>
      </div>

      {/* 批量導入對話框 */}
      {showBulkImport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <UniversalBulkImport
              dataType="activities-with-participants"
              sessions={sessions}
              selectedSessionId={selectedSessionId}
              onImport={handleBulkImport}
              onCancel={() => setShowBulkImport(false)}
            />
          </div>
        </div>
      )}

      {/* 搜索和過濾 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索活動</label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="輸入活動名稱、描述或日期"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">委員會過濾</label>
          <select
            value={committeeFilter}
            onChange={(e) => setCommitteeFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
          >
            <option value="">全部委員會</option>
            {committees.map((committee) => (
              <option key={committee} value={committee}>
                {committee}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 活動列表 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort("date")}
                >
                  <div className="flex items-center">
                    日期
                    {getSortIcon("date")}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    活動名稱
                    {getSortIcon("name")}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort("committee")}
                >
                  <div className="flex items-center">
                    委員會
                    {getSortIcon("committee")}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort("participants")}
                >
                  <div className="flex items-center">
                    參加人數
                    {getSortIcon("participants")}
                  </div>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              {sortedActivities.length > 0 ? (
                sortedActivities.map((activity) => (
                  <tr
                    key={activity.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-650 cursor-pointer"
                    onClick={() => onSelectActivity(activity.id)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {activity.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {activity.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      <span
                        className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          activity.committee === "學術委員會"
                            ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                            : activity.committee === "活動委員會"
                              ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                              : activity.committee === "總務委員會"
                                ? "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200"
                                : activity.committee === "公關委員會"
                                  ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                                  : "bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200",
                        )}
                      >
                        {activity.committee}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {activity.participants.length}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setEditingActivity(activity)
                        }}
                        className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                      >
                        編輯
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          if (window.confirm(`確定要刪除活動 "${activity.name}" 嗎？`)) {
                            onDeleteActivity(activity.id)
                          }
                        }}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        刪除
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    沒有找到符合條件的活動
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 新增活動表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {editingActivity ? "編輯活動" : "新增活動"}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">日期</label>
            <input
              type="date"
              value={editingActivity ? editingActivity.date : newActivity.date}
              onChange={(e) =>
                editingActivity
                  ? setEditingActivity({ ...editingActivity, date: e.target.value })
                  : setNewActivity({ ...newActivity, date: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">活動名稱</label>
            <input
              type="text"
              value={editingActivity ? editingActivity.name : newActivity.name}
              onChange={(e) =>
                editingActivity
                  ? setEditingActivity({ ...editingActivity, name: e.target.value })
                  : setNewActivity({ ...newActivity, name: e.target.value })
              }
              placeholder="輸入活動名稱"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">委員會</label>
            <select
              value={editingActivity ? editingActivity.committee : newActivity.committee}
              onChange={(e) =>
                editingActivity
                  ? setEditingActivity({ ...editingActivity, committee: e.target.value })
                  : setNewActivity({ ...newActivity, committee: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            >
              {committees.map((committee) => (
                <option key={committee} value={committee}>
                  {committee}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">活動描述</label>
            <input
              type="text"
              value={editingActivity ? editingActivity.description || "" : newActivity.description}
              onChange={(e) =>
                editingActivity
                  ? setEditingActivity({ ...editingActivity, description: e.target.value })
                  : setNewActivity({ ...newActivity, description: e.target.value })
              }
              placeholder="輸入活動描述（選填）"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div className="mt-4 flex justify-end">
          {editingActivity ? (
            <>
              <button
                onClick={() => setEditingActivity(null)}
                className="mr-2 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleUpdateActivity}
                className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
                disabled={!editingActivity.date || !editingActivity.name || !editingActivity.committee}
              >
                更新活動
              </button>
            </>
          ) : (
            <button
              onClick={handleAddActivity}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
              disabled={!newActivity.date || !newActivity.name || !newActivity.committee}
            >
              新增活動
            </button>
          )}
        </div>
      </div>

      {/* 導出對話框 */}
      {showExportDialog && (
        <ExportDialog activities={activities} allParticipants={[]} onClose={() => setShowExportDialog(false)} />
      )}
    </div>
  )
}
