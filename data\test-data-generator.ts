import type { Activity, Participant } from '../types'

// 生成測試用的參加者姓名
const generateNames = (count: number): string[] => {
  const surnames = ['張', '李', '王', '劉', '陳', '楊', '趙', '黃', '周', '吳', '徐', '孫', '胡', '朱', '高', '林', '何', '郭', '馬', '羅']
  const givenNames = ['偉', '芳', '娜', '秀英', '敏', '靜', '麗', '強', '磊', '軍', '洋', '勇', '艷', '傑', '娟', '濤', '明', '超', '秀蘭', '霞']
  
  const names: string[] = []
  for (let i = 0; i < count; i++) {
    const surname = surnames[Math.floor(Math.random() * surnames.length)]
    const givenName = givenNames[Math.floor(Math.random() * givenNames.length)]
    const number = String(i + 1).padStart(3, '0')
    names.push(`${surname}${givenName}${number}`)
  }
  return names
}

// 生成測試活動日期
const generateActivityDates = (startDate: string, endDate: string, count: number): string[] => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const dates: string[] = []
  
  const totalDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
  const interval = Math.floor(totalDays / count)
  
  for (let i = 0; i < count; i++) {
    const date = new Date(start.getTime() + (i * interval * 24 * 60 * 60 * 1000))
    dates.push(date.toISOString().split('T')[0])
  }
  
  return dates
}

// 生成當月活動日期
const generateCurrentMonthActivityDates = (count: number): string[] => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()
  
  // 獲取當月第一天和最後一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  const dates: string[] = []
  const totalDays = lastDay.getDate()
  
  // 在當月內均勻分布活動日期
  for (let i = 0; i < count; i++) {
    const dayOfMonth = Math.floor((i * totalDays) / count) + 1
    const date = new Date(year, month, dayOfMonth)
    dates.push(date.toISOString().split('T')[0])
  }
  
  // 去重並排序
  const uniqueDates = [...new Set(dates)].sort()
  
  // 如果去重後數量不足，補充隨機日期
  while (uniqueDates.length < count) {
    const randomDay = Math.floor(Math.random() * totalDays) + 1
    const randomDate = new Date(year, month, randomDay).toISOString().split('T')[0]
    if (!uniqueDates.includes(randomDate)) {
      uniqueDates.push(randomDate)
    }
  }
  
  return uniqueDates.sort().slice(0, count)
}

// 生成參加者數據
export const generateTestParticipants = (count: number = 500, activityDates: string[]): Participant[] => {
  const names = generateNames(count)
  const categories = ['核心成員', '一般成員', '新成員', '榮譽成員']
  const categoryWeights = [0.1, 0.4, 0.4, 0.1] // 各類別的比例
  
  return names.map((name, index) => {
    // 根據權重分配類別
    let categoryIndex = 0
    const random = Math.random()
    let cumulative = 0
    for (let i = 0; i < categoryWeights.length; i++) {
      cumulative += categoryWeights[i]
      if (random <= cumulative) {
        categoryIndex = i
        break
      }
    }
    
    const category = categories[categoryIndex]
    const attendance: Record<string, boolean> = {}
    const registration: Record<string, boolean> = {}
    
    // 為每個活動生成報名和出席數據
    activityDates.forEach(date => {
      // 報名率：核心成員90%，一般成員70%，新成員50%，榮譽成員30%
      const registrationRates = { '核心成員': 0.9, '一般成員': 0.7, '新成員': 0.5, '榮譽成員': 0.3 }
      const registrationRate = registrationRates[category as keyof typeof registrationRates] || 0.6
      
      const isRegistered = Math.random() < registrationRate
      registration[date] = isRegistered
      
      if (isRegistered) {
        // 出席率：核心成員85%，一般成員75%，新成員65%，榮譽成員80%
        const attendanceRates = { '核心成員': 0.85, '一般成員': 0.75, '新成員': 0.65, '榮譽成員': 0.8 }
        const attendanceRate = attendanceRates[category as keyof typeof attendanceRates] || 0.7
        
        attendance[date] = Math.random() < attendanceRate
      } else {
        // 未報名則不出席
        attendance[date] = false
      }
    })
    
    return {
      id: String(index + 1),
      name,
      category,
      attendance,
      registration
    }
  })
}

// 生成測試活動數據
export const generateTestActivities = (dates: string[], participants: Participant[]): Activity[] => {
  const committees = ['學術委員會', '活動委員會', '總務委員會', '公關委員會', '技術委員會']
  const activityTypes = ['會議', '講座', '工作坊', '聚會', '培訓', '研討會', '交流會', '座談會', '分享會', '討論會']
  const topics = ['人工智能', '區塊鏈', '數據科學', '軟件工程', '網絡安全', '雲計算', '物聯網', '機器學習', '前端開發', '後端開發']
  
  return dates.map((date, index) => {
    const committee = committees[index % committees.length]
    const type = activityTypes[index % activityTypes.length]
    const topic = topics[index % topics.length]
    
    // 為每個活動分配參加者
    const activityParticipants = participants.map(participant => ({
      id: participant.id,
      name: participant.name,
      category: participant.category,
      attendance: { [date]: participant.attendance[date] || false },
      registration: { [date]: participant.registration[date] || false }
    }))
    
    return {
      id: String(index + 1),
      name: `${topic}${type}`,
      date,
      committee,
      participants: activityParticipants
    }
  })
}

// 生成完整的測試數據集
export const generateTestDataSet = (participantCount: number = 500, activityCount: number = 50) => {
  const activityDates = generateActivityDates('2024-01-01', '2024-12-31', activityCount)
  const participants = generateTestParticipants(participantCount, activityDates)
  const activities = generateTestActivities(activityDates, participants)
  
  return {
    participants,
    activities,
    activityDates
  }
}

// 生成當月測試數據集（新增功能）
export const generateCurrentMonthTestData = (activityCount: number = 100, participantCount: number = 200) => {
  console.log(`正在生成當月測試數據：${activityCount}個活動，${participantCount}位參與者`)
  
  const activityDates = generateCurrentMonthActivityDates(activityCount)
  const participants = generateTestParticipants(participantCount, activityDates)
  const activities = generateTestActivities(activityDates, participants)
  
  // 計算統計信息
  const stats = calculateTestDataStats(participants, activities)
  
  console.log('測試數據生成完成：')
  console.log(`- 活動數量：${stats.totalActivities}`)
  console.log(`- 參與者數量：${stats.totalParticipants}`)
  console.log(`- 總報名率：${stats.registrationRate}%`)
  console.log(`- 總出席率：${stats.attendanceRate}%`)
  console.log(`- 活動日期範圍：${activityDates[0]} 至 ${activityDates[activityDates.length - 1]}`)
  
  return {
    participants,
    activities,
    activityDates,
    stats
  }
}

// 生成大量測試數據（用於壓力測試）
export const generateLargeTestDataSet = (activityCount: number = 200, participantCount: number = 1000) => {
  console.log(`正在生成大量測試數據：${activityCount}個活動，${participantCount}位參與者`)
  
  const currentYear = new Date().getFullYear()
  const startDate = `${currentYear}-01-01`
  const endDate = `${currentYear}-12-31`
  
  const activityDates = generateActivityDates(startDate, endDate, activityCount)
  const participants = generateTestParticipants(participantCount, activityDates)
  const activities = generateTestActivities(activityDates, participants)
  
  const stats = calculateTestDataStats(participants, activities)
  
  console.log('大量測試數據生成完成：')
  console.log(`- 活動數量：${stats.totalActivities}`)
  console.log(`- 參與者數量：${stats.totalParticipants}`)
  console.log(`- 總報名率：${stats.registrationRate}%`)
  console.log(`- 總出席率：${stats.attendanceRate}%`)
  
  return {
    participants,
    activities,
    activityDates,
    stats
  }
}

// 計算測試數據的統計信息
export const calculateTestDataStats = (participants: Participant[], activities: Activity[]) => {
  let totalRegistrations = 0
  let totalAttendances = 0
  let totalNoShows = 0
  let totalPossibleRegistrations = 0
  
  activities.forEach(activity => {
    activity.participants.forEach(participant => {
      totalPossibleRegistrations++
      
      const isRegistered = participant.registration[activity.date]
      const hasAttended = participant.attendance[activity.date]
      
      if (isRegistered) {
        totalRegistrations++
        if (hasAttended) {
          totalAttendances++
        } else {
          totalNoShows++
        }
      }
    })
  })
  
  const registrationRate = totalPossibleRegistrations > 0 ? (totalRegistrations / totalPossibleRegistrations) * 100 : 0
  const attendanceRate = totalRegistrations > 0 ? (totalAttendances / totalRegistrations) * 100 : 0
  const noShowRate = totalRegistrations > 0 ? (totalNoShows / totalRegistrations) * 100 : 0
  
  return {
    totalParticipants: participants.length,
    totalActivities: activities.length,
    totalPossibleRegistrations,
    totalRegistrations,
    totalAttendances,
    totalNoShows,
    registrationRate: Math.round(registrationRate * 100) / 100,
    attendanceRate: Math.round(attendanceRate * 100) / 100,
    noShowRate: Math.round(noShowRate * 100) / 100
  }
}