"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import type { Activity, Participant } from "../types"
import { BulkImport } from "./bulk-import"

interface ParticipantManagementProps {
  activity: Activity
  allParticipants: Participant[] // 所有可能的參加者
  onAddParticipant: (activityId: string, participant: Omit<Participant, "id" | "attendance">) => void
  onRemoveParticipant: (activityId: string, participantId: string) => void
  onUpdateParticipant: (activityId: string, participant: Participant) => void
  onToggleAttendance: (activityId: string, participantId: string) => void
  onBack: () => void
  onBulkAddParticipants: (activityId: string, participants: Omit<Participant, "id" | "attendance">[]) => void
}

export function ParticipantManagement({
  activity,
  allParticipants,
  onAddParticipant,
  onRemoveParticipant,
  onUpdateParticipant,
  onToggleAttendance,
  onBack,
  onBulkAddParticipants,
}: ParticipantManagementProps) {
  const [newParticipant, setNewParticipant] = useState({
    name: "",
    category: "",
  })
  const [editingParticipant, setEditingParticipant] = useState<Participant | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("")
  const [showBulkImport, setShowBulkImport] = useState(false)
  // 在現有的 state 聲明中添加新的狀態變量
  const [existingParticipantSearchTerm, setExistingParticipantSearchTerm] = useState("")
  const [existingParticipantCategoryFilter, setExistingParticipantCategoryFilter] = useState("")

  // 獲取所有參加者類別
  const allCategories = Array.from(
    new Set([
      ...allParticipants.map((p) => p.category).filter(Boolean),
      ...activity.participants.map((p) => p.category).filter(Boolean),
    ]),
  ) as string[]

  // 處理新增參加者
  const handleAddParticipant = () => {
    if (newParticipant.name) {
      onAddParticipant(activity.id, newParticipant)
      setNewParticipant({
        name: "",
        category: "",
      })
    }
  }

  // 處理更新參加者
  const handleUpdateParticipant = () => {
    if (editingParticipant) {
      onUpdateParticipant(activity.id, editingParticipant)
      setEditingParticipant(null)
    }
  }

  // 處理批量導入參加者
  const handleBulkImport = (participants: Omit<Participant, "id" | "attendance">[]) => {
    onBulkAddParticipants(activity.id, participants)
    setShowBulkImport(false)
  }

  // 過濾參加者
  const filteredParticipants = activity.participants.filter((participant) => {
    const matchesSearch = participant.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter ? participant.category === categoryFilter : true
    return matchesSearch && matchesCategory
  })

  // 獲取未參加此活動的參加者
  const nonParticipants = allParticipants.filter((p) => !activity.participants.some((ap) => ap.id === p.id))

  // 修改 nonParticipants 的篩選邏輯，添加搜索和類別篩選
  const filteredNonParticipants = nonParticipants.filter((participant) => {
    const matchesSearch = participant.name.toLowerCase().includes(existingParticipantSearchTerm.toLowerCase())
    const matchesCategory = existingParticipantCategoryFilter
      ? participant.category === existingParticipantCategoryFilter
      : true
    return matchesSearch && matchesCategory
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
        >
          ← 返回活動列表
        </button>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {activity.name} ({activity.date})
        </h2>
      </div>

      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">活動詳情</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">日期:</span> {activity.date}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">委員會:</span> {activity.committee}
            </p>
            {activity.description && (
              <p className="text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium">描述:</span> {activity.description}
              </p>
            )}
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">參加者統計</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">總參加人數:</span> {activity.participants.length}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">出席人數:</span>{" "}
              {activity.participants.filter((p) => p.attendance[activity.date]).length}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">出席率:</span>{" "}
              {activity.participants.length > 0
                ? (
                    (activity.participants.filter((p) => p.attendance[activity.date]).length /
                      activity.participants.length) *
                    100
                  ).toFixed(1)
                : "0"}
              %
            </p>
          </div>
        </div>
      </div>

      {/* 搜索和過濾 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索參加者</label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="輸入參加者姓名"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">類別過濾</label>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
          >
            <option value="">全部類別</option>
            {allCategories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 參加者列表 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  姓名
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  類別
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  出席狀態
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              {filteredParticipants.length > 0 ? (
                filteredParticipants.map((participant) => (
                  <tr key={participant.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.category || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => onToggleAttendance(activity.id, participant.id)}
                        className={cn(
                          "w-6 h-6 rounded-full",
                          participant.attendance[activity.date]
                            ? "bg-green-500 hover:bg-green-600"
                            : "bg-red-500 hover:bg-red-600",
                        )}
                        title={participant.attendance[activity.date] ? "出席" : "缺席"}
                      >
                        {participant.attendance[activity.date] ? "✓" : "✗"}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setEditingParticipant(participant)}
                        className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                      >
                        編輯
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`確定要從活動中移除 "${participant.name}" 嗎？`)) {
                            onRemoveParticipant(activity.id, participant.id)
                          }
                        }}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        移除
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    沒有找到符合條件的參加者
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 新增參加者表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        {!showBulkImport && (
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {editingParticipant ? "編輯參加者" : "新增參加者"}
            </h3>
            <button
              onClick={() => setShowBulkImport(true)}
              className="px-3 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-200 rounded-md hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors text-sm flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                />
              </svg>
              批量導入
            </button>
          </div>
        )}
        {showBulkImport ? (
          <BulkImport onImport={handleBulkImport} onCancel={() => setShowBulkImport(false)} />
        ) : (
          <>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {editingParticipant ? "編輯參加者" : "新增參加者"}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
                <input
                  type="text"
                  value={editingParticipant ? editingParticipant.name : newParticipant.name}
                  onChange={(e) =>
                    editingParticipant
                      ? setEditingParticipant({ ...editingParticipant, name: e.target.value })
                      : setNewParticipant({ ...newParticipant, name: e.target.value })
                  }
                  placeholder="輸入參加者姓名"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">類別</label>
                <select
                  value={editingParticipant ? editingParticipant.category || "" : newParticipant.category}
                  onChange={(e) =>
                    editingParticipant
                      ? setEditingParticipant({ ...editingParticipant, category: e.target.value })
                      : setNewParticipant({ ...newParticipant, category: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                >
                  <option value="">選擇類別</option>
                  {allCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                  <option value="新增類別">+ 新增類別</option>
                </select>
              </div>
              {(editingParticipant?.category === "新增類別" || newParticipant.category === "新增類別") && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">新類別名稱</label>
                  <input
                    type="text"
                    value={
                      editingParticipant?.category === "新增類別"
                        ? ""
                        : newParticipant.category === "新增類別"
                          ? ""
                          : ""
                    }
                    onChange={(e) =>
                      editingParticipant
                        ? setEditingParticipant({ ...editingParticipant, category: e.target.value })
                        : setNewParticipant({ ...newParticipant, category: e.target.value })
                    }
                    placeholder="輸入新類別名稱"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
              )}
            </div>
            <div className="mt-4 flex justify-end">
              {editingParticipant ? (
                <>
                  <button
                    onClick={() => setEditingParticipant(null)}
                    className="mr-2 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleUpdateParticipant}
                    className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
                    disabled={!editingParticipant.name}
                  >
                    更新參加者
                  </button>
                </>
              ) : (
                <button
                  onClick={handleAddParticipant}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                  disabled={!newParticipant.name}
                >
                  新增參加者
                </button>
              )}
            </div>
          </>
        )}
      </div>

      {/* 從現有參加者中添加 */}
      {nonParticipants.length > 0 && (
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">從現有參加者中添加</h3>

          {/* 篩選控件 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索現有參加者</label>
              <input
                type="text"
                value={existingParticipantSearchTerm}
                onChange={(e) => setExistingParticipantSearchTerm(e.target.value)}
                placeholder="輸入參加者姓名"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">類別篩選</label>
              <select
                value={existingParticipantCategoryFilter}
                onChange={(e) => setExistingParticipantCategoryFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部類別</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 參加者列表 */}
          {filteredNonParticipants.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {filteredNonParticipants.map((participant) => (
                <div
                  key={participant.id}
                  className="p-2 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 cursor-pointer"
                  onClick={() => {
                    onAddParticipant(activity.id, {
                      name: participant.name,
                      category: participant.category,
                    })
                  }}
                >
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{participant.name}</p>
                  {participant.category && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">{participant.category}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              {nonParticipants.length > 0 ? "沒有找到符合篩選條件的參加者" : "沒有可添加的參加者"}
            </div>
          )}

          {/* 顯示篩選結果計數 */}
          {nonParticipants.length > 0 && (
            <div className="mt-4 text-sm text-gray-500 dark:text-gray-400 text-right">
              顯示 {filteredNonParticipants.length} / {nonParticipants.length} 位參加者
            </div>
          )}
        </div>
      )}
    </div>
  )
}
