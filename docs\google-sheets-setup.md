# Google Sheets 整合設置指南

本指南將幫助你設置 HKUYA 出席管理系統與 Google Sheets 的整合。

## 📋 前置要求

1. Google 帳戶
2. Google Cloud Platform 專案
3. Google Sheets API 啟用
4. 服務帳戶或 OAuth2 認證設置

## 🚀 快速開始

### 方法一：Service Account（推薦用於生產環境）

#### 步驟 1：創建 Google Cloud 專案

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 創建新專案或選擇現有專案
3. 記下專案 ID

#### 步驟 2：啟用 Google Sheets API

1. 在 Google Cloud Console 中，前往「API 和服務」>「庫」
2. 搜索「Google Sheets API」
3. 點擊啟用

#### 步驟 3：創建服務帳戶

1. 前往「API 和服務」>「憑證」
2. 點擊「創建憑證」>「服務帳戶」
3. 填寫服務帳戶詳細信息
4. 點擊「創建並繼續」
5. 跳過角色分配（可選）
6. 點擊「完成」

#### 步驟 4：生成服務帳戶金鑰

1. 在憑證頁面，找到剛創建的服務帳戶
2. 點擊服務帳戶電子郵件
3. 前往「金鑰」標籤
4. 點擊「新增金鑰」>「創建新金鑰」
5. 選擇「JSON」格式
6. 下載 JSON 文件

#### 步驟 5：創建 Google Sheets

1. 前往 [Google Sheets](https://sheets.google.com/)
2. 創建新的試算表
3. 從 URL 中提取試算表 ID：
   ```
   https://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit
   ```
4. 將服務帳戶的電子郵件添加為試算表的編輯者：
   - 點擊「共享」
   - 輸入服務帳戶電子郵件
   - 設置權限為「編輯者」
   - 點擊「傳送」

#### 步驟 6：配置環境變數

創建 `.env.local` 文件：

```env
# Google Sheets API Configuration
NEXT_PUBLIC_GOOGLE_SPREADSHEET_ID=your-spreadsheet-id-here
NEXT_PUBLIC_GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
NEXT_PUBLIC_GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
NEXT_PUBLIC_GOOGLE_PROJECT_ID=your-google-project-id

# Sheet Names
NEXT_PUBLIC_PARTICIPANTS_SHEET_NAME=參加者資料
NEXT_PUBLIC_ACTIVITIES_SHEET_NAME=活動資料
NEXT_PUBLIC_ATTENDANCE_SHEET_NAME=出席記錄
NEXT_PUBLIC_SESSIONS_SHEET_NAME=屆別資料
```

### 方法二：OAuth2（適用於開發環境）

#### 步驟 1-2：同上

#### 步驟 3：創建 OAuth2 客戶端

1. 前往「API 和服務」>「憑證」
2. 點擊「創建憑證」>「OAuth 客戶端 ID」
3. 選擇應用程式類型：「網頁應用程式」
4. 設置授權重定向 URI：
   - `http://localhost:3000` (開發環境)
   - `https://your-domain.com` (生產環境)
5. 點擊「創建」
6. 記下客戶端 ID 和客戶端密鑰

#### 步驟 4：配置環境變數

```env
# Google Sheets API Configuration (OAuth2)
NEXT_PUBLIC_GOOGLE_SPREADSHEET_ID=your-spreadsheet-id-here
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
NEXT_PUBLIC_GOOGLE_CLIENT_SECRET=your-client-secret
NEXT_PUBLIC_GOOGLE_PROJECT_ID=your-google-project-id
```

## 📊 Google Sheets 數據結構

系統會自動創建以下工作表：

### 參加者資料
| 欄位 | 說明 |
|------|------|
| ID | 唯一識別碼 |
| 姓名 | 參加者姓名 |
| 職銜 | 參加者職銜 |
| 電子郵件 | 聯絡電子郵件 |
| 電話 | 聯絡電話 |
| 加入日期 | 加入日期 |
| 是否活躍 | 活躍狀態 |
| 備註 | 額外備註 |
| 創建時間 | 記錄創建時間 |
| 更新時間 | 記錄更新時間 |

### 活動資料
| 欄位 | 說明 |
|------|------|
| ID | 唯一識別碼 |
| 活動名稱 | 活動名稱 |
| 日期 | 活動日期 |
| 地點 | 活動地點 |
| 描述 | 活動描述 |
| 委員會 | 所屬委員會 |
| 類型 | 活動類型 |
| 最大參與人數 | 參與人數限制 |
| 是否活躍 | 活躍狀態 |
| 創建時間 | 記錄創建時間 |
| 更新時間 | 記錄更新時間 |

### 出席記錄
| 欄位 | 說明 |
|------|------|
| ID | 唯一識別碼 |
| 參加者ID | 參加者識別碼 |
| 參加者姓名 | 參加者姓名 |
| 活動ID | 活動識別碼 |
| 活動名稱 | 活動名稱 |
| 出席狀態 | present/absent/late/excused |
| 簽到時間 | 簽到時間戳 |
| 備註 | 額外備註 |
| 記錄者 | 記錄者 |
| 創建時間 | 記錄創建時間 |
| 更新時間 | 記錄更新時間 |

### 屆別資料
| 欄位 | 說明 |
|------|------|
| ID | 唯一識別碼 |
| 屆別名稱 | 屆別名稱 |
| 開始日期 | 屆別開始日期 |
| 結束日期 | 屆別結束日期 |
| 描述 | 屆別描述 |
| 是否活躍 | 活躍狀態 |
| 創建時間 | 記錄創建時間 |
| 更新時間 | 記錄更新時間 |

## 🔧 使用方法

1. **連接 Google Sheets**：
   - 在應用中點擊「Google Sheets」標籤
   - 配置 API 設置
   - 點擊「測試連接」確認設置正確
   - 點擊「連接」建立連接

2. **同步數據**：
   - 手動同步：點擊「同步」按鈕
   - 自動同步：啟用自動同步功能

3. **監控狀態**：
   - 查看連接狀態指示器
   - 檢查同步歷史和錯誤信息

## 🛠️ 故障排除

### 常見問題

1. **連接失敗**：
   - 檢查 API 金鑰是否正確
   - 確認 Google Sheets API 已啟用
   - 驗證試算表 ID 是否正確

2. **權限錯誤**：
   - 確認服務帳戶有試算表的編輯權限
   - 檢查 OAuth2 範圍設置

3. **同步失敗**：
   - 檢查網絡連接
   - 驗證數據格式是否正確
   - 查看錯誤日誌

### 調試技巧

1. 開啟瀏覽器開發者工具查看控制台錯誤
2. 檢查網絡請求是否成功
3. 驗證環境變數是否正確設置

## 🔒 安全注意事項

1. **保護 API 金鑰**：
   - 不要將私鑰提交到版本控制
   - 使用環境變數存儲敏感信息
   - 定期輪換 API 金鑰

2. **最小權限原則**：
   - 只授予必要的 Google Sheets 權限
   - 限制服務帳戶的訪問範圍

3. **數據安全**：
   - 定期備份重要數據
   - 監控異常訪問活動

## 📚 相關資源

- [Google Sheets API 文檔](https://developers.google.com/sheets/api)
- [Google Cloud Console](https://console.cloud.google.com/)
- [服務帳戶文檔](https://cloud.google.com/iam/docs/service-accounts)
- [OAuth2 設置指南](https://developers.google.com/identity/protocols/oauth2)
