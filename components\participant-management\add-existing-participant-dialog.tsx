"use client"

import { useState, useMemo } from "react"
import type { Participant } from "../../types"

interface AddExistingParticipantDialogProps {
  availableParticipants: Participant[]
  allCategories: string[]
  onAdd: (participants: Set<string>) => void
  onClose: () => void
}

export function AddExistingParticipantDialog({
  availableParticipants,
  allCategories,
  onAdd,
  onClose,
}: AddExistingParticipantDialogProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("")
  const [selectedParticipants, setSelectedParticipants] = useState<Set<string>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 20

  // 過濾和分頁的可用參加者
  const filteredAvailableParticipants = useMemo(() => {
    const filtered = availableParticipants.filter((participant) => {
      const matchesSearch = participant.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = categoryFilter ? participant.category === categoryFilter : true
      return matchesSearch && matchesCategory
    })

    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return {
      participants: filtered.slice(startIndex, endIndex),
      total: filtered.length,
      totalPages: Math.ceil(filtered.length / pageSize),
    }
  }, [availableParticipants, searchTerm, categoryFilter, currentPage])

  const handleSelectParticipant = (participantId: string) => {
    const newSelected = new Set(selectedParticipants)
    if (newSelected.has(participantId)) {
      newSelected.delete(participantId)
    } else {
      newSelected.add(participantId)
    }
    setSelectedParticipants(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedParticipants.size === filteredAvailableParticipants.participants.length) {
      setSelectedParticipants(new Set())
    } else {
      const allIds = new Set(filteredAvailableParticipants.participants.map((p) => p.id))
      setSelectedParticipants(allIds)
    }
  }

  const handleAdd = () => {
    onAdd(selectedParticipants)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-600">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">添加現有參加者到當前屆別</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          {/* 搜索和過濾 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索參加者</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1)
                }}
                placeholder="輸入姓名搜索"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜過濾</label>
              <select
                value={categoryFilter}
                onChange={(e) => {
                  setCategoryFilter(e.target.value)
                  setCurrentPage(1)
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部職銜</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 統計信息 */}
          <div className="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
            <span>
              找到 {filteredAvailableParticipants.total} 位可添加的參加者
              {selectedParticipants.size > 0 && ` | 已選擇 ${selectedParticipants.size} 位`}
            </span>
            <button
              onClick={handleSelectAll}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              {selectedParticipants.size === filteredAvailableParticipants.participants.length ? "取消全選" : "全選當前頁"}
            </button>
          </div>

          {/* 參加者列表 */}
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden max-h-96 overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    選擇
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    姓名
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    職銜
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {filteredAvailableParticipants.participants.map((participant) => (
                  <tr
                    key={participant.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-650 cursor-pointer"
                    onClick={() => handleSelectParticipant(participant.id)}
                  >
                    <td className="px-4 py-3 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedParticipants.has(participant.id)}
                        onChange={() => handleSelectParticipant(participant.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.name}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.category || "未設定"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 分頁控制 */}
          {filteredAvailableParticipants.totalPages > 1 && (
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                第 {currentPage} 頁，共 {filteredAvailableParticipants.totalPages} 頁
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一頁
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1))}
                  disabled={currentPage === filteredAvailableParticipants.totalPages}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一頁
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 底部操作按鈕 */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleAdd}
            disabled={selectedParticipants.size === 0}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            添加選中的參加者 ({selectedParticipants.size})
          </button>
        </div>
      </div>
    </div>
  )
}
