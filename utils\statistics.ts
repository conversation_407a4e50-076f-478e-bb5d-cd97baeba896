import type { Activity, Participant } from "../types"

export interface ParticipantStatistic {
  id: string
  name: string
  category?: string
  totalActivities: number
  attendedActivities: number
  attendanceRate: number
  recentTrend: number // 最近5次活動的趨勢
  consecutiveAbsences: number
  participationRate: number // 新增：參與度（參與活動數/總活動數）
  sessionParticipationRates: Record<string, number> // 新增：各屆別參與度
  // 新增：詳細參與度統計
  participationDetails: {
    totalParticipated: number // 總參與活動數
    totalAvailable: number // 總可參與活動數
    participationRatio: string // 參與比率字符串 (如 "60/150")
  }
  sessionParticipationDetails: Record<string, {
    participated: number // 該屆別參與活動數
    available: number // 該屆別總活動數
    participationRatio: string // 該屆別參與比率字符串 (如 "10/50")
  }>
  isHighParticipation: boolean // 是否為高參與度成員
}

export interface ActivityStatistic {
  id: string
  name: string
  date: string
  committee: string // 只保留委員會
  totalParticipants: number
  attendedParticipants: number
  attendanceRate: number
}

export interface OverallStatistics {
  totalActivities: number
  totalParticipants: number
  averageAttendanceRate: number
  totalAttendanceRecords: number
  totalPossibleAttendance: number
  participantStats: ParticipantStatistic[]
  activityStats: ActivityStatistic[]
  categoryStats: Record<
    string,
    {
      count: number
      averageRate: number
      totalActivities: number
      totalAttended: number
    }
  >
  monthlyTrends: Array<{
    month: string
    rate: number
    activities: number
  }>
  participationStats: {
    averageParticipationRate: number
    sessionParticipationStats: Record<string, {
      sessionName: string
      averageParticipationRate: number
      totalActivities: number
      activeParticipants: number
    }>
    highParticipationStats: {
      threshold: number // 高參與度閾值
      count: number // 高參與度成員數量
      percentage: number // 高參與度成員百分比
    }
  }
}

export function calculateStatistics(activities: Activity[], allParticipants: Participant[], sessions?: Session[], highParticipationThreshold: number = 70): OverallStatistics {
  // 計算參加者統計
  const participantStats: ParticipantStatistic[] = allParticipants.map((participant) => {
    const participantActivities = activities.filter((activity) =>
      activity.participants.some((p) => p.id === participant.id),
    )

    // 只計算報名的活動
    const registeredActivities = participantActivities.filter((activity) => {
      const participantInActivity = activity.participants.find((p) => p.id === participant.id)
      return participantInActivity?.registration?.[activity.date] === true
    })

    const attendedActivities = registeredActivities.filter((activity) => {
      const participantInActivity = activity.participants.find((p) => p.id === participant.id)
      return participantInActivity?.attendance[activity.date] === true
    })

    // 出席率基於報名的活動計算
    const attendanceRate =
      registeredActivities.length > 0 ? (attendedActivities.length / registeredActivities.length) * 100 : 0

    // 計算整體參與度（參與活動數/總活動數）
    const participationRate = activities.length > 0 ? (participantActivities.length / activities.length) * 100 : 0

    // 計算各屆別參與度
    const sessionParticipationRates: Record<string, number> = {}
    const sessionParticipationDetails: Record<string, {
      participated: number
      available: number
      participationRatio: string
    }> = {}
    
    if (sessions) {
      sessions.forEach((session) => {
        const sessionActivities = activities.filter((activity) => activity.sessionId === session.id)
        const sessionParticipantActivities = sessionActivities.filter((activity) =>
          activity.participants.some((p) => p.id === participant.id)
        )
        const participated = sessionParticipantActivities.length
        const available = sessionActivities.length
        
        sessionParticipationRates[session.id] = available > 0 
          ? (participated / available) * 100 
          : 0
          
        sessionParticipationDetails[session.id] = {
          participated,
          available,
          participationRatio: `${participated}/${available}`
        }
      })
    }
    
    // 計算總參與度詳細資訊
    const totalParticipated = participantActivities.length
    const totalAvailable = activities.length
    const participationDetails = {
      totalParticipated,
      totalAvailable,
      participationRatio: `${totalParticipated}/${totalAvailable}`
    }
    
    // 判斷是否為高參與度成員
    const isHighParticipation = participationRate >= highParticipationThreshold

    // 計算最近5次報名活動的趨勢
    const recentRegisteredActivities = registeredActivities
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5)

    let recentTrend = 0
    if (recentRegisteredActivities.length >= 3) {
      const firstHalf = recentRegisteredActivities.slice(Math.ceil(recentRegisteredActivities.length / 2))
      const secondHalf = recentRegisteredActivities.slice(0, Math.floor(recentRegisteredActivities.length / 2))

      const firstHalfRate =
        firstHalf.length > 0
          ? (firstHalf.filter((activity) => {
              const p = activity.participants.find((p) => p.id === participant.id)
              return p?.attendance[activity.date]
            }).length /
              firstHalf.length) *
            100
          : 0

      const secondHalfRate =
        secondHalf.length > 0
          ? (secondHalf.filter((activity) => {
              const p = activity.participants.find((p) => p.id === participant.id)
              return p?.attendance[activity.date]
            }).length /
              secondHalf.length) *
            100
          : 0

      recentTrend = secondHalfRate - firstHalfRate
    }

    // 計算連續缺席次數（基於報名但未出席）
    const sortedActivities = participantActivities.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    )
    let consecutiveAbsences = 0
    for (const activity of sortedActivities) {
      const participantInActivity = activity.participants.find((p) => p.id === participant.id)
      const registered = participantInActivity?.registration?.[activity.date] || false
      const attended = participantInActivity?.attendance[activity.date] || false
      
      // 只有報名但未出席才算缺席
      if (registered && !attended) {
        consecutiveAbsences++
      } else {
        break
      }
    }

    return {
      id: participant.id,
      name: participant.name,
      category: participant.category,
      totalActivities: registeredActivities.length, // 改為報名的活動數
      attendedActivities: attendedActivities.length,
      attendanceRate,
      recentTrend,
      consecutiveAbsences,
      participationRate,
      sessionParticipationRates,
      participationDetails,
      sessionParticipationDetails,
      isHighParticipation,
    }
  })

  // 計算活動統計
  const activityStats: ActivityStatistic[] = activities.map((activity) => {
    const attendedParticipants = activity.participants.filter((p) => p.attendance[activity.date] === true)
    const attendanceRate =
      activity.participants.length > 0 ? (attendedParticipants.length / activity.participants.length) * 100 : 0

    return {
      id: activity.id,
      name: activity.name,
      date: activity.date,
      committee: activity.committee,
      totalParticipants: activity.participants.length,
      attendedParticipants: attendedParticipants.length,
      attendanceRate,
    }
  })

  // 計算類別統計
  const categoryStats: Record<string, any> = {}
  allParticipants.forEach((participant) => {
    if (participant.category) {
      if (!categoryStats[participant.category]) {
        categoryStats[participant.category] = {
          count: 0,
          totalActivities: 0,
          totalAttended: 0,
          averageRate: 0,
        }
      }

      const participantStat = participantStats.find((ps) => ps.id === participant.id)
      if (participantStat) {
        categoryStats[participant.category].count++
        categoryStats[participant.category].totalActivities += participantStat.totalActivities
        categoryStats[participant.category].totalAttended += participantStat.attendedActivities
      }
    }
  })

  // 計算每個類別的平均出席率
  Object.keys(categoryStats).forEach((category) => {
    const stats = categoryStats[category]
    stats.averageRate = stats.totalActivities > 0 ? (stats.totalAttended / stats.totalActivities) * 100 : 0
  })

  // 計算月度趨勢
  const monthlyData: Record<string, { attended: number; total: number; activities: number }> = {}

  activities.forEach((activity) => {
    const monthKey = activity.date.substring(0, 7) // YYYY-MM
    if (!monthlyData[monthKey]) {
      monthlyData[monthKey] = { attended: 0, total: 0, activities: 0 }
    }

    const attendedCount = activity.participants.filter((p) => p.attendance[activity.date]).length
    const registeredCount = activity.participants.filter((p) => p.registration[activity.date]).length
    monthlyData[monthKey].attended += attendedCount
    monthlyData[monthKey].total += registeredCount
    monthlyData[monthKey].activities++
  })

  const monthlyTrends = Object.entries(monthlyData)
    .map(([month, data]) => ({
      month,
      rate: data.total > 0 ? (data.attended / data.total) * 100 : 0,
      activities: data.activities,
    }))
    .sort((a, b) => a.month.localeCompare(b.month))

  // 計算整體統計
  const totalAttendanceRecords = activities.reduce(
    (sum, activity) => sum + activity.participants.filter((p) => p.attendance[activity.date]).length,
    0,
  )

  // 只計算已報名的參加者作為可能出席人數
  const totalPossibleAttendance = activities.reduce(
    (sum, activity) => sum + activity.participants.filter((p) => p.registration[activity.date]).length,
    0,
  )

  const averageAttendanceRate =
    totalPossibleAttendance > 0 ? (totalAttendanceRecords / totalPossibleAttendance) * 100 : 0

  // 計算參與度統計
  const averageParticipationRate = participantStats.length > 0 
    ? participantStats.reduce((sum, p) => sum + p.participationRate, 0) / participantStats.length 
    : 0

  const sessionParticipationStats: Record<string, {
    sessionName: string
    averageParticipationRate: number
    totalActivities: number
    activeParticipants: number
  }> = {}

  if (sessions) {
    sessions.forEach((session) => {
      const sessionActivities = activities.filter((activity) => activity.sessionId === session.id)
      const sessionParticipants = participantStats.filter((p) => p.sessionParticipationRates[session.id] > 0)
      
      sessionParticipationStats[session.id] = {
        sessionName: session.name,
        averageParticipationRate: sessionParticipants.length > 0
          ? sessionParticipants.reduce((sum, p) => sum + p.sessionParticipationRates[session.id], 0) / sessionParticipants.length
          : 0,
        totalActivities: sessionActivities.length,
        activeParticipants: sessionParticipants.length,
      }
    })
  }

  // 計算高參與度統計
  const highParticipationCount = participantStats.filter(p => p.isHighParticipation).length
  const highParticipationStats = {
    threshold: highParticipationThreshold,
    count: highParticipationCount,
    percentage: participantStats.length > 0 ? (highParticipationCount / participantStats.length) * 100 : 0
  }

  return {
    totalActivities: activities.length,
    totalParticipants: allParticipants.length,
    averageAttendanceRate,
    totalAttendanceRecords,
    totalPossibleAttendance,
    participantStats,
    activityStats,
    categoryStats,
    monthlyTrends,
    participationStats: {
      averageParticipationRate,
      sessionParticipationStats,
      highParticipationStats,
    },
  }
}
