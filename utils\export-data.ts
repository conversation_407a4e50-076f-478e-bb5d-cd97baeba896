import Papa from "papaparse"
import * as XLSX from "xlsx"
import type { Activity, Participant } from "../types"
import { calculateStatistics, type OverallStatistics } from "./statistics"

// 導出格式類型
export type ExportFormat = "csv" | "excel"

// 導出數據類型
export type ExportDataType = "overall" | "participants" | "activities" | "categories" | "all"

// 準備參加者統計數據
function prepareParticipantData(stats: OverallStatistics): Record<string, any>[] {
  return stats.participantStats.map((participant) => ({
    ID: participant.id,
    姓名: participant.name,
    類別: participant.category || "-",
    參與活動數: participant.totalActivities,
    出席次數: participant.attendedActivities,
    出席率: `${participant.attendanceRate.toFixed(1)}%`,
    趨勢:
      participant.recentTrend > 0
        ? `上升 ${participant.recentTrend.toFixed(1)}%`
        : participant.recentTrend < 0
          ? `下降 ${Math.abs(participant.recentTrend).toFixed(1)}%`
          : "持平",
    連續缺席: participant.consecutiveAbsences,
  }))
}

// 準備活動統計數據
function prepareActivityData(stats: OverallStatistics): Record<string, any>[] {
  return stats.activityStats.map((activity) => ({
    ID: activity.id,
    活動名稱: activity.name,
    日期: activity.date,
    類型: activity.type,
    參加人數: activity.totalParticipants,
    出席人數: activity.attendedParticipants,
    出席率: `${activity.attendanceRate.toFixed(1)}%`,
  }))
}

// 準備類別統計數據
function prepareCategoryData(stats: OverallStatistics): Record<string, any>[] {
  return Object.entries(stats.categoryStats).map(([category, stats]) => ({
    類別: category,
    人數: stats.count,
    平均出席率: `${stats.averageRate.toFixed(1)}%`,
    總出席次數: stats.totalAttended,
    總活動參與次數: stats.totalActivities,
  }))
}

// 準備月度趨勢數據
function prepareMonthlyTrendData(stats: OverallStatistics): Record<string, any>[] {
  return stats.monthlyTrends.map((trend) => ({
    月份: trend.month,
    出席率: `${trend.rate.toFixed(1)}%`,
    活動數: trend.activities,
  }))
}

// 準備整體統計數據
function prepareOverallData(stats: OverallStatistics): Record<string, any>[] {
  return [
    {
      指標: "總活動數",
      數值: stats.totalActivities,
    },
    {
      指標: "總參加者數",
      數值: stats.totalParticipants,
    },
    {
      指標: "平均出席率",
      數值: `${stats.averageAttendanceRate.toFixed(1)}%`,
    },
    {
      指標: "總出席人次",
      數值: stats.totalAttendanceRecords,
    },
    {
      指標: "總可能出席人次",
      數值: stats.totalPossibleAttendance,
    },
  ]
}

// 導出為CSV
function exportToCSV(data: Record<string, any>[], filename: string): void {
  const csv = Papa.unparse(data)
  const blob = new Blob(["\ufeff" + csv], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)

  const link = document.createElement("a")
  link.setAttribute("href", url)
  link.setAttribute("download", `${filename}.csv`)
  link.style.visibility = "hidden"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 導出為Excel
function exportToExcel(data: Record<string, any>[][], sheetNames: string[], filename: string): void {
  const wb = XLSX.utils.book_new()

  data.forEach((sheetData, index) => {
    if (sheetData.length > 0) {
      const ws = XLSX.utils.json_to_sheet(sheetData)
      XLSX.utils.book_append_sheet(wb, ws, sheetNames[index])
    }
  })

  XLSX.writeFile(wb, `${filename}.xlsx`)
}

// 主導出函數
export function exportStatistics(
  activities: Activity[],
  allParticipants: Participant[],
  format: ExportFormat = "excel",
  dataType: ExportDataType = "all",
): void {
  const stats = calculateStatistics(activities, allParticipants)
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-").substring(0, 19)

  if (format === "csv") {
    // CSV格式只能導出單個表格，所以根據dataType選擇導出不同的數據
    switch (dataType) {
      case "overall":
        exportToCSV(prepareOverallData(stats), `整體統計_${timestamp}`)
        break
      case "participants":
        exportToCSV(prepareParticipantData(stats), `參加者統計_${timestamp}`)
        break
      case "activities":
        exportToCSV(prepareActivityData(stats), `活動統計_${timestamp}`)
        break
      case "categories":
        exportToCSV(prepareCategoryData(stats), `類別統計_${timestamp}`)
        break
      case "all":
        // 如果選擇全部，則分別導出多個CSV文件
        exportToCSV(prepareOverallData(stats), `整體統計_${timestamp}`)
        exportToCSV(prepareParticipantData(stats), `參加者統計_${timestamp}`)
        exportToCSV(prepareActivityData(stats), `活動統計_${timestamp}`)
        exportToCSV(prepareCategoryData(stats), `類別統計_${timestamp}`)
        exportToCSV(prepareMonthlyTrendData(stats), `月度趨勢_${timestamp}`)
        break
    }
  } else {
    // Excel格式可以在一個文件中包含多個表格
    const sheetData: Record<string, any>[][] = []
    const sheetNames: string[] = []

    if (dataType === "overall" || dataType === "all") {
      sheetData.push(prepareOverallData(stats))
      sheetNames.push("整體統計")

      sheetData.push(prepareMonthlyTrendData(stats))
      sheetNames.push("月度趨勢")
    }

    if (dataType === "participants" || dataType === "all") {
      sheetData.push(prepareParticipantData(stats))
      sheetNames.push("參加者統計")
    }

    if (dataType === "activities" || dataType === "all") {
      sheetData.push(prepareActivityData(stats))
      sheetNames.push("活動統計")
    }

    if (dataType === "categories" || dataType === "all") {
      sheetData.push(prepareCategoryData(stats))
      sheetNames.push("類別統計")
    }

    exportToExcel(sheetData, sheetNames, `出席統計_${timestamp}`)
  }
}

// 導出原始數據
export function exportRawData(
  activities: Activity[],
  allParticipants: Participant[],
  format: ExportFormat = "excel",
): void {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-").substring(0, 19)

  // 準備參加者原始數據
  const participantsData = allParticipants.map((p) => ({
    ID: p.id,
    姓名: p.name,
    類別: p.category || "-",
  }))

  // 準備活動原始數據
  const activitiesData = activities.map((a) => ({
    ID: a.id,
    活動名稱: a.name,
    日期: a.date,
    類型: a.type,
    描述: a.description || "-",
    參加人數: a.participants.length,
  }))

  // 準備出席記錄數據
  const attendanceData: Record<string, any>[] = []
  activities.forEach((activity) => {
    activity.participants.forEach((participant) => {
      attendanceData.push({
        活動ID: activity.id,
        活動名稱: activity.name,
        活動日期: activity.date,
        參加者ID: participant.id,
        參加者姓名: participant.name,
        出席狀態: participant.attendance[activity.date] ? "出席" : "缺席",
      })
    })
  })

  if (format === "csv") {
    exportToCSV(participantsData, `參加者數據_${timestamp}`)
    exportToCSV(activitiesData, `活動數據_${timestamp}`)
    exportToCSV(attendanceData, `出席記錄_${timestamp}`)
  } else {
    const sheetData = [participantsData, activitiesData, attendanceData]
    const sheetNames = ["參加者數據", "活動數據", "出席記錄"]

    exportToExcel(sheetData, sheetNames, `原始數據_${timestamp}`)
  }
}
