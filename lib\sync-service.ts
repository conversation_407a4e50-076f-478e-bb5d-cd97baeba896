// 數據同步服務
import { GoogleSheetsService } from './google-sheets'
import type {
  Participant,
  Activity,
  Session,
  SessionParticipant,
  AttendanceRecord
} from '../types'
import type {
  ParticipantSheetRow,
  ActivitySheetRow,
  AttendanceSheetRow,
  SessionSheetRow,
  SessionParticipantSheetRow,
  SyncResult,
  SyncOptions,
  GoogleSheetsApiResponse
} from '../types/google-sheets'

export class SyncService {
  private googleSheetsService: GoogleSheetsService

  constructor(googleSheetsService: GoogleSheetsService) {
    this.googleSheetsService = googleSheetsService
  }

  /**
   * 同步參加者數據到 Google Sheets
   */
  async syncParticipantsToSheets(participants: Participant[]): Promise<SyncResult> {
    try {
      const sheetRows: string[][] = participants.map(participant => [
        participant.id,
        participant.name,
        participant.category || '',
        '', // email
        '', // phone
        new Date().toISOString().split('T')[0], // joinDate
        'true', // isActive
        '', // notes
        new Date().toISOString(), // createdAt
        new Date().toISOString()  // updatedAt
      ])

      // 清空現有數據並寫入新數據
      await this.googleSheetsService.clearSheet('參加者資料')
      const result = await this.googleSheetsService.appendData('參加者資料', sheetRows)

      return {
        success: result.success,
        message: result.success ? `成功同步 ${participants.length} 位參加者` : result.error || '同步失敗',
        recordsAffected: participants.length,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('同步參加者數據時發生錯誤:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }

  /**
   * 從 Google Sheets 讀取參加者數據
   */
  async syncParticipantsFromSheets(): Promise<GoogleSheetsApiResponse<Participant[]>> {
    try {
      const result = await this.googleSheetsService.readRange('參加者資料', 'A2:J1000')
      
      if (!result.success || !result.data) {
        return {
          success: false,
          error: result.error || '無法讀取數據',
          timestamp: new Date()
        }
      }

      const participants: Participant[] = result.data
        .filter(row => row.length > 0 && row[0]) // 過濾空行
        .map(row => ({
          id: row[0] || '',
          name: row[1] || '',
          category: row[2] || '',
          attendance: {} // 初始化為空對象
        }))

      return {
        success: true,
        data: participants,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('從 Google Sheets 讀取參加者數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 同步活動數據到 Google Sheets
   */
  async syncActivitiesToSheets(activities: Activity[]): Promise<SyncResult> {
    try {
      const sheetRows: string[][] = activities.map(activity => [
        activity.id,
        activity.name,
        activity.date,
        activity.location || '',
        activity.description || '',
        activity.committee,
        activity.type,
        activity.maxParticipants?.toString() || '',
        'true', // isActive
        new Date().toISOString(), // createdAt
        new Date().toISOString()  // updatedAt
      ])

      await this.googleSheetsService.clearSheet('活動資料')
      const result = await this.googleSheetsService.appendData('活動資料', sheetRows)

      return {
        success: result.success,
        message: result.success ? `成功同步 ${activities.length} 個活動` : result.error || '同步失敗',
        recordsAffected: activities.length,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('同步活動數據時發生錯誤:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }

  /**
   * 從 Google Sheets 讀取活動數據
   */
  async syncActivitiesFromSheets(): Promise<GoogleSheetsApiResponse<Activity[]>> {
    try {
      const result = await this.googleSheetsService.readRange('活動資料', 'A2:K1000')
      
      if (!result.success || !result.data) {
        return {
          success: false,
          error: result.error || '無法讀取數據',
          timestamp: new Date()
        }
      }

      const activities: Activity[] = result.data
        .filter(row => row.length > 0 && row[0])
        .map(row => ({
          id: row[0] || '',
          name: row[1] || '',
          date: row[2] || '',
          location: row[3] || '',
          description: row[4] || '',
          committee: row[5] || '',
          type: row[6] || '',
          maxParticipants: row[7] ? parseInt(row[7]) : undefined,
          participants: [] // 初始化為空數組
        }))

      return {
        success: true,
        data: activities,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('從 Google Sheets 讀取活動數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 同步屆別數據到 Google Sheets
   */
  async syncSessionsToSheets(sessions: Session[]): Promise<SyncResult> {
    try {
      const sheetRows: string[][] = sessions.map(session => [
        session.id,
        session.name,
        session.startDate,
        session.endDate,
        session.description || '',
        session.isActive ? 'true' : 'false',
        new Date().toISOString(), // createdAt
        new Date().toISOString()  // updatedAt
      ])

      await this.googleSheetsService.clearSheet('屆別資料')
      const result = await this.googleSheetsService.appendData('屆別資料', sheetRows)

      return {
        success: result.success,
        message: result.success ? `成功同步 ${sessions.length} 個屆別` : result.error || '同步失敗',
        recordsAffected: sessions.length,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('同步屆別數據時發生錯誤:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }

  /**
   * 從 Google Sheets 讀取屆別數據
   */
  async syncSessionsFromSheets(): Promise<GoogleSheetsApiResponse<Session[]>> {
    try {
      const result = await this.googleSheetsService.readRange('屆別資料', 'A2:H1000')
      
      if (!result.success || !result.data) {
        return {
          success: false,
          error: result.error || '無法讀取數據',
          timestamp: new Date()
        }
      }

      const sessions: Session[] = result.data
        .filter(row => row.length > 0 && row[0])
        .map(row => ({
          id: row[0] || '',
          name: row[1] || '',
          startDate: row[2] || '',
          endDate: row[3] || '',
          description: row[4] || '',
          isActive: row[5] === 'true'
        }))

      return {
        success: true,
        data: sessions,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('從 Google Sheets 讀取屆別數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 同步出席記錄到 Google Sheets
   */
  async syncAttendanceToSheets(attendanceRecords: AttendanceRecord[]): Promise<SyncResult> {
    try {
      const sheetRows: string[][] = attendanceRecords.map(record => [
        `${record.participantId}-${record.activityId}`, // 組合ID
        record.participantId,
        record.participantName || '',
        record.activityId,
        record.activityName || '',
        record.status,
        record.checkInTime || '',
        record.notes || '',
        record.recordedBy || '',
        new Date().toISOString(), // createdAt
        new Date().toISOString()  // updatedAt
      ])

      await this.googleSheetsService.clearSheet('出席記錄')
      const result = await this.googleSheetsService.appendData('出席記錄', sheetRows)

      return {
        success: result.success,
        message: result.success ? `成功同步 ${attendanceRecords.length} 條出席記錄` : result.error || '同步失敗',
        recordsAffected: attendanceRecords.length,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('同步出席記錄時發生錯誤:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }

  /**
   * 執行完整同步
   */
  async performFullSync(data: {
    participants: Participant[]
    activities: Activity[]
    sessions: Session[]
    attendanceRecords: AttendanceRecord[]
  }, options: SyncOptions = {}): Promise<SyncResult> {
    try {
      const results: SyncResult[] = []

      if (!options.sheets || options.sheets.includes('participants')) {
        const participantResult = await this.syncParticipantsToSheets(data.participants)
        results.push(participantResult)
      }

      if (!options.sheets || options.sheets.includes('activities')) {
        const activityResult = await this.syncActivitiesToSheets(data.activities)
        results.push(activityResult)
      }

      if (!options.sheets || options.sheets.includes('sessions')) {
        const sessionResult = await this.syncSessionsToSheets(data.sessions)
        results.push(sessionResult)
      }

      if (!options.sheets || options.sheets.includes('attendance')) {
        const attendanceResult = await this.syncAttendanceToSheets(data.attendanceRecords)
        results.push(attendanceResult)
      }

      const totalRecords = results.reduce((sum, result) => sum + result.recordsAffected, 0)
      const allSuccessful = results.every(result => result.success)

      return {
        success: allSuccessful,
        message: allSuccessful 
          ? `完整同步成功，共處理 ${totalRecords} 條記錄`
          : `部分同步失敗，請檢查錯誤信息`,
        recordsAffected: totalRecords,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('執行完整同步時發生錯誤:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }
}
