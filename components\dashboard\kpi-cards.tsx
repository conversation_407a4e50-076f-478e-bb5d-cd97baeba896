"use client"

import { cn } from "@/lib/utils"

interface KPICardsProps {
  kpis: {
    thisMonthActivities: number
    lastMonthActivities: number
    thisMonthAvgAttendance: number
    attendanceChange: number
    engagementRate: number
    activeParticipants: number
  }
  averageAttendanceRate: number
  allParticipantsCount: number
}

export function KPICards({ kpis, averageAttendanceRate, allParticipantsCount }: KPICardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="card-professional bg-gradient-to-br from-primary to-primary/80 text-primary-content animate-slideUp">
        <div className="card-body p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold opacity-90 tracking-wide">本月活動數</h3>
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold tracking-tight">{kpis.thisMonthActivities}</p>
            <div className="text-right">
              <p className="text-xs opacity-75 mb-1">上月: {kpis.lastMonthActivities}</p>
              <div className={cn(
                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                kpis.thisMonthActivities >= kpis.lastMonthActivities
                  ? "bg-success/20 text-success-content"
                  : "bg-error/20 text-error-content"
              )}>
                {kpis.thisMonthActivities >= kpis.lastMonthActivities ? "↗" : "↘"}
                {Math.abs(kpis.thisMonthActivities - kpis.lastMonthActivities)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="card-professional bg-gradient-to-br from-success to-success/80 text-success-content animate-slideUp" style={{animationDelay: '0.1s'}}>
        <div className="card-body p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold opacity-90 tracking-wide">本月平均出席率</h3>
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold tracking-tight">{kpis.thisMonthAvgAttendance}%</p>
            <div className="text-right">
              <div className={cn(
                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                kpis.attendanceChange >= 0
                  ? "bg-success-content/20 text-success-content"
                  : "bg-error/20 text-error-content"
              )}>
                {kpis.attendanceChange >= 0 ? "↗" : "↘"} {Math.abs(kpis.attendanceChange).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="card-professional bg-gradient-to-br from-accent to-accent/80 text-accent-content animate-slideUp" style={{animationDelay: '0.2s'}}>
        <div className="card-body p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold opacity-90 tracking-wide">參與度</h3>
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold tracking-tight">{kpis.engagementRate}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75 mb-1">{kpis.activeParticipants}/{allParticipantsCount}</p>
              <p className="text-xs font-medium opacity-75">活躍參與者</p>
            </div>
          </div>
        </div>
      </div>

      <div className="card-professional bg-gradient-to-br from-info to-info/80 text-info-content animate-slideUp" style={{animationDelay: '0.3s'}}>
        <div className="card-body p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold opacity-90 tracking-wide">整體出席率</h3>
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold tracking-tight">{averageAttendanceRate.toFixed(1)}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75 mb-1">目標: 80%</p>
              <div className={cn(
                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                averageAttendanceRate >= 80
                  ? "bg-success/20 text-success-content"
                  : "bg-warning/20 text-warning-content"
              )}>
                {averageAttendanceRate >= 80 ? "✓ 達標" : "⚠ 待改善"}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
