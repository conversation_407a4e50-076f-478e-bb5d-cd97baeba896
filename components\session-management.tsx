"use client"

import { useState } from "react"
import type { Session } from "../types"

interface SessionManagementProps {
  sessions: Session[]
  onAddSession: (session: Omit<Session, "id">) => void
  onEditSession: (session: Session) => void
  onDeleteSession: (sessionId: string) => void
  onSetActiveSession: (sessionId: string) => void
  onBack: () => void
}

export function SessionManagement({
  sessions,
  onAddSession,
  onEditSession,
  onDeleteSession,
  onSetActiveSession,
  onBack,
}: SessionManagementProps) {
  const [newSession, setNewSession] = useState({
    name: "",
    startDate: "",
    endDate: "",
    isActive: false,
    committees: ["學術委員會", "活動委員會", "總務委員會", "公關委員會"],
  })
  const [editingSession, setEditingSession] = useState<Session | null>(null)
  const [searchTerm, setSearchTerm] = useState("")

  // 處理新增屆別
  const handleAddSession = () => {
    if (newSession.name && newSession.startDate && newSession.endDate) {
      onAddSession(newSession)
      setNewSession({
        name: "",
        startDate: "",
        endDate: "",
        isActive: false,
        committees: ["學術委員會", "活動委員會", "總務委員會", "公關委員會"],
      })
    }
  }

  // 處理更新屆別
  const handleUpdateSession = () => {
    if (editingSession) {
      onEditSession(editingSession)
      setEditingSession(null)
    }
  }

  // 處理刪除屆別
  const handleDeleteSession = (sessionId: string) => {
    if (window.confirm("確定要刪除此屆別嗎？這將刪除所有相關的活動數據。")) {
      onDeleteSession(sessionId)
    }
  }

  // 添加委員會到當前編輯的屆別
  const addCommitteeToSession = (committee: string) => {
    if (editingSession && !editingSession.committees.includes(committee)) {
      setEditingSession({
        ...editingSession,
        committees: [...editingSession.committees, committee],
      })
    } else if (!newSession.committees.includes(committee)) {
      setNewSession({
        ...newSession,
        committees: [...newSession.committees, committee],
      })
    }
  }

  // 從屆別中移除委員會
  const removeCommitteeFromSession = (committee: string, isEditing: boolean) => {
    if (isEditing && editingSession) {
      setEditingSession({
        ...editingSession,
        committees: editingSession.committees.filter((c) => c !== committee),
      })
    } else {
      setNewSession({
        ...newSession,
        committees: newSession.committees.filter((c) => c !== committee),
      })
    }
  }

  // 過濾屆別
  const filteredSessions = sessions.filter((session) => session.name.toLowerCase().includes(searchTerm.toLowerCase()))

  // 按時間排序屆別（最新的在前面）
  const sortedSessions = [...filteredSessions].sort(
    (a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime(),
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
        >
          ← 返回
        </button>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">屆別管理</h2>
        <div className="w-20"></div>
      </div>

      {/* 搜索 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">搜索屆別</label>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="輸入屆別名稱"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
        />
      </div>

      {/* 屆別列表 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">屆別列表 ({sortedSessions.length})</h3>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-600">
          {sortedSessions.length > 0 ? (
            sortedSessions.map((session) => (
              <div key={session.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-650">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white">{session.name}</h4>
                      {session.isActive && (
                        <span className="ml-2 px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded-full">
                          當前屆別
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {session.startDate} 至 {session.endDate}
                    </p>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">委員會：</p>
                      <div className="flex flex-wrap gap-1">
                        {session.committees.map((committee) => (
                          <span
                            key={committee}
                            className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded"
                          >
                            {committee}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {!session.isActive && (
                      <button
                        onClick={() => onSetActiveSession(session.id)}
                        className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 text-sm"
                      >
                        設為當前
                      </button>
                    )}
                    <button
                      onClick={() => setEditingSession(session)}
                      className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm"
                    >
                      編輯
                    </button>
                    <button
                      onClick={() => handleDeleteSession(session.id)}
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 text-sm"
                    >
                      刪除
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">沒有找到符合條件的屆別</p>
            </div>
          )}
        </div>
      </div>

      {/* 新增/編輯屆別表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {editingSession ? "編輯屆別" : "新增屆別"}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">屆別名稱</label>
            <input
              type="text"
              value={editingSession ? editingSession.name : newSession.name}
              onChange={(e) =>
                editingSession
                  ? setEditingSession({ ...editingSession, name: e.target.value })
                  : setNewSession({ ...newSession, name: e.target.value })
              }
              placeholder="例如：第三十一屆"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={editingSession ? editingSession.isActive : newSession.isActive}
                onChange={(e) =>
                  editingSession
                    ? setEditingSession({ ...editingSession, isActive: e.target.checked })
                    : setNewSession({ ...newSession, isActive: e.target.checked })
                }
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">設為當前屆別</span>
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">開始日期</label>
            <input
              type="date"
              value={editingSession ? editingSession.startDate : newSession.startDate}
              onChange={(e) =>
                editingSession
                  ? setEditingSession({ ...editingSession, startDate: e.target.value })
                  : setNewSession({ ...newSession, startDate: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">結束日期</label>
            <input
              type="date"
              value={editingSession ? editingSession.endDate : newSession.endDate}
              onChange={(e) =>
                editingSession
                  ? setEditingSession({ ...editingSession, endDate: e.target.value })
                  : setNewSession({ ...newSession, endDate: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* 委員會管理 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">委員會</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {(editingSession ? editingSession.committees : newSession.committees).map((committee) => (
              <span
                key={committee}
                className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded flex items-center"
              >
                {committee}
                <button
                  onClick={() => removeCommitteeFromSession(committee, !!editingSession)}
                  className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex space-x-2">
            <input
              type="text"
              placeholder="新增委員會"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  const input = e.target as HTMLInputElement
                  if (input.value.trim()) {
                    addCommitteeToSession(input.value.trim())
                    input.value = ""
                  }
                }
              }}
            />
            <button
              onClick={(e) => {
                const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement
                if (input.value.trim()) {
                  addCommitteeToSession(input.value.trim())
                  input.value = ""
                }
              }}
              className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
              添加
            </button>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          {editingSession && (
            <button
              onClick={() => setEditingSession(null)}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              取消
            </button>
          )}
          <button
            onClick={editingSession ? handleUpdateSession : handleAddSession}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            disabled={
              editingSession
                ? !editingSession.name || !editingSession.startDate || !editingSession.endDate
                : !newSession.name || !newSession.startDate || !newSession.endDate
            }
          >
            {editingSession ? "更新屆別" : "新增屆別"}
          </button>
        </div>
      </div>

      {/* 統計信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">統計信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{sessions.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">總屆別數</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {sessions.filter((s) => s.isActive).length}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">當前屆別</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {Math.max(...sessions.map((s) => s.committees.length), 0)}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">最多委員會數</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{filteredSessions.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">搜索結果</p>
          </div>
        </div>
      </div>
    </div>
  )
}
