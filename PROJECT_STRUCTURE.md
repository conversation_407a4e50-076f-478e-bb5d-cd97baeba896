# HKUYA 出席管理系統 - 項目結構文檔

## 項目概述

這是一個基於 Next.js 和 TypeScript 開發的出席管理系統，專為香港青年聯會（HKUYA）設計。系統支持多屆別管理、活動出席追蹤、統計分析和數據導出等功能。

## 技術棧

- **框架**: Next.js 15.2.4
- **語言**: TypeScript
- **樣式**: TailwindCSS
- **圖表**: Recharts
- **文件處理**: XLSX, PapaParse
- **UI組件**: Lucide React (圖標)

## 項目結構

```
attendance/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局樣式
│   ├── layout.tsx         # 根佈局
│   └── page.tsx           # 主頁面
├── components/            # React 組件
├── data/                  # 初始數據
├── hooks/                 # 自定義 Hooks
├── lib/                   # 工具庫
├── types.ts              # TypeScript 類型定義
├── utils/                 # 工具函數
└── attendance-tracker.tsx # 主應用組件
```

## 核心組件詳解

### 1. 主應用組件

#### `attendance-tracker.tsx`
- **功能**: 應用的主入口組件，管理整體狀態和路由
- **主要特性**:
  - 統一狀態管理（活動、參加者、委員會、屆別）
  - 視圖模式切換（儀表板、活動管理、統計等）
  - 整合所有自定義 Hooks
  - 活躍等級設定管理

### 2. 核心功能組件

#### `components/dashboard.tsx`
- **功能**: 系統主儀表板
- **主要特性**:
  - 整體統計概覽（總活動數、參加者數、平均出席率）
  - 活躍等級分佈圖表
  - 委員會出席率對比
  - 月度趨勢分析
  - 快速操作入口
  - 數據導出功能
  - 活躍等級設定

#### `components/activity-management.tsx`
- **功能**: 活動管理界面
- **主要特性**:
  - 新增/編輯/刪除活動
  - 活動列表展示和搜索
  - 委員會篩選
  - 批量導入活動
  - 活動統計信息
  - 屆別關聯管理

#### `components/participant-management.tsx`
- **功能**: 特定活動的參加者管理
- **主要特性**:
  - 添加/移除參加者
  - 出席狀態切換
  - 參加者信息編輯
  - 批量導入參加者
  - 從現有參加者中選擇
  - 搜索和篩選功能

#### `components/participant-management-page.tsx`
- **功能**: 全局參加者管理頁面
- **主要特性**:
  - 所有參加者的統一管理
  - 參加者出席歷史查看
  - 跨活動統計分析
  - 屆別關聯管理

#### `components/statistics-dashboard.tsx`
- **功能**: 詳細統計分析儀表板
- **主要特性**:
  - 多維度統計圖表
  - 屆別對比分析
  - 自定義日期範圍
  - 排行榜顯示
  - 委員會統計
  - 趨勢分析

#### `components/committee-management.tsx`
- **功能**: 委員會管理
- **主要特性**:
  - 新增/編輯/刪除委員會
  - 委員會列表管理
  - 批量導入委員會
  - 屆別關聯

#### `components/session-management.tsx`
- **功能**: 屆別管理
- **主要特性**:
  - 新增/編輯/刪除屆別
  - 屆別狀態管理（活躍/非活躍）
  - 屆別時間範圍設定
  - 委員會配置

### 3. 輔助功能組件

#### `components/bulk-import.tsx`
- **功能**: 批量導入參加者
- **支持格式**: CSV, Excel
- **特性**: 文件預覽、錯誤處理、數據驗證

#### `components/universal-bulk-import.tsx`
- **功能**: 通用批量導入組件
- **用途**: 支持活動、委員會等多種數據類型的批量導入

#### `components/export-dialog.tsx`
- **功能**: 數據導出對話框
- **支持格式**: CSV, Excel
- **導出類型**: 統計數據、原始數據
- **數據範圍**: 全部、參加者、活動、類別

#### `components/participant-attendance-history.tsx`
- **功能**: 參加者出席歷史詳情
- **特性**: 
  - 跨屆別出席記錄
  - 出席趨勢分析
  - 詳細統計信息

#### `components/activity-level-settings.tsx`
- **功能**: 活躍等級設定
- **特性**: 自定義活躍度閾值設定

### 4. 自定義 Hooks

#### `hooks/use-attendance-data.ts`
- **功能**: 統一數據狀態管理
- **管理數據**: 活動、參加者、委員會、屆別、屆別參加者
- **計算屬性**: 當前屆別相關數據

#### `hooks/use-activity-handlers.ts`
- **功能**: 活動相關操作處理
- **操作**: 新增、編輯、刪除活動，管理活動參加者

#### `hooks/use-participant-handlers.ts`
- **功能**: 參加者相關操作處理
- **操作**: 新增、編輯、刪除參加者，出席狀態管理

#### `hooks/use-committee-handlers.ts`
- **功能**: 委員會相關操作處理
- **操作**: 新增、編輯、刪除委員會

#### `hooks/use-session-handlers.ts`
- **功能**: 屆別相關操作處理
- **操作**: 新增、編輯、刪除屆別，屆別狀態管理

### 5. 工具函數

#### `utils/statistics.ts`
- **功能**: 統計計算核心邏輯
- **計算項目**:
  - 參加者統計（出席率、趨勢、連續缺席）
  - 活動統計（出席率、參與度）
  - 類別統計（分組分析）
  - 月度趨勢分析

#### `utils/export-data.ts`
- **功能**: 數據導出處理
- **支持格式**: CSV, Excel
- **導出類型**: 統計數據、原始數據
- **數據處理**: 格式化、本地化

#### `utils/file-parsers.ts`
- **功能**: 文件解析處理
- **支持格式**: CSV, Excel, TXT
- **特性**: 錯誤處理、數據驗證

#### `utils/activity-level.ts`
- **功能**: 活躍等級計算
- **等級**: 非常活躍、活躍、不活躍
- **計算**: 基於出席率的等級判定

### 6. 數據結構

#### `types.ts`
定義了系統的核心數據類型：

- **Activity**: 活動信息
- **Participant**: 參加者信息
- **Session**: 屆別信息
- **SessionParticipant**: 屆別參加者關聯
- **RegistrationStatus**: 報名狀態枚舉
- **ActivityLevel**: 活躍等級枚舉
- **ActivityLevelSettings**: 活躍等級設定

#### `data/initial-data.ts`
- **功能**: 初始化數據
- **包含**: 示例活動、參加者、委員會、屆別數據
- **用途**: 系統演示和測試

## 主要功能特性

### 1. 多屆別管理
- 支持多個屆別的並行管理
- 屆別間數據隔離和關聯
- 活躍屆別切換

### 2. 活動管理
- 完整的活動生命週期管理
- 委員會分類
- 批量操作支持

### 3. 出席追蹤
- 實時出席狀態更新
- 報名與出席狀態分離
- 歷史記錄追蹤

### 4. 統計分析
- 多維度統計圖表
- 趨勢分析
- 活躍度評估
- 排行榜功能

### 5. 數據管理
- 批量導入/導出
- 多格式支持
- 數據驗證

### 6. 用戶體驗
- 響應式設計
- 深色模式支持
- 直觀的操作界面
- 實時數據更新

## 開發和部署

### 開發命令
```bash
npm run dev    # 開發服務器
npm run build  # 構建生產版本
npm run start  # 啟動生產服務器
npm run lint   # 代碼檢查
```

### 部署
- 支持 Vercel 自動部署
- 與 v0.dev 集成
- 自動同步代碼變更

## 總結

這個出席管理系統是一個功能完整、結構清晰的 React 應用，採用了現代化的開發模式和最佳實踐。系統具有良好的可擴展性和維護性，能夠滿足組織的出席管理需求。