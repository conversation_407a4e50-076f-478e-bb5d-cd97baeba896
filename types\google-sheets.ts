// Google Sheets 相關類型定義

export interface GoogleSheetsConfig {
  spreadsheetId: string
  serviceAccountEmail?: string
  privateKey?: string
  projectId?: string
  clientId?: string
  clientSecret?: string
}

export interface SheetNames {
  participants: string
  activities: string
  attendance: string
  sessions: string
}

export interface SyncStatus {
  isConnected: boolean
  lastSyncTime: Date | null
  syncInProgress: boolean
  error: string | null
  autoSyncEnabled: boolean
}

export interface SyncResult {
  success: boolean
  message: string
  recordsAffected: number
  timestamp: Date
}

// Google Sheets 行數據格式
export interface ParticipantSheetRow {
  id: string
  name: string
  category: string
  email?: string
  phone?: string
  joinDate: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface ActivitySheetRow {
  id: string
  name: string
  date: string
  location?: string
  description?: string
  committee: string
  type: string
  maxParticipants?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface AttendanceSheetRow {
  id: string
  participantId: string
  participantName: string
  activityId: string
  activityName: string
  status: 'present' | 'absent' | 'late' | 'excused'
  checkInTime?: string
  notes?: string
  recordedBy?: string
  createdAt: string
  updatedAt: string
}

// 出席記錄類型（用於應用內部）
export interface AttendanceRecord {
  participantId: string
  participantName?: string
  activityId: string
  activityName?: string
  status: 'present' | 'absent' | 'late' | 'excused'
  checkInTime?: string
  notes?: string
  recordedBy?: string
}

export interface SessionSheetRow {
  id: string
  name: string
  startDate: string
  endDate: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface SessionParticipantSheetRow {
  id: string
  sessionId: string
  sessionName: string
  participantId: string
  participantName: string
  category: string
  joinDate: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// API 響應類型
export interface GoogleSheetsApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: Date
}

// 批量操作類型
export interface BatchOperation {
  operation: 'create' | 'update' | 'delete'
  sheetName: string
  data: any[]
}

export interface BatchResult {
  successful: number
  failed: number
  errors: string[]
}

// 同步配置
export interface SyncOptions {
  forceSync?: boolean
  direction?: 'push' | 'pull' | 'bidirectional'
  sheets?: string[]
  batchSize?: number
}

// 連接狀態
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  SYNCING = 'syncing'
}

export interface ConnectionState {
  status: ConnectionStatus
  lastConnected?: Date
  error?: string
  retryCount: number
}
