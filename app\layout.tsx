import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'HKUYA 出席管理系統',
  description: '香港青年聯會專業出席管理系統 - 高效管理活動出席、統計分析與數據導出',
  keywords: ['出席管理', '活動管理', 'HKUYA', '香港青年聯會', '統計分析'],
  authors: [{ name: 'HKUYA Tech Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-HK" data-theme="professional" className="scroll-smooth">
      <body className="bg-base-100 text-base-content antialiased">
        {children}
      </body>
    </html>
  )
}
