import type { ActivityLevel, ActivityLevelSettings } from "../types"

// 預設活躍等級設定
export const DEFAULT_ACTIVITY_LEVEL_SETTINGS: ActivityLevelSettings = {
  veryActive: 80,
  active: 40,
}

// 根據出席率判斷活躍等級
export function getActivityLevel(attendanceRate: number, settings: ActivityLevelSettings): ActivityLevel {
  if (attendanceRate >= settings.veryActive) {
    return "very_active"
  } else if (attendanceRate >= settings.active) {
    return "active"
  } else {
    return "inactive"
  }
}

// 獲取活躍等級的顯示文字
export function getActivityLevelText(level: ActivityLevel): string {
  switch (level) {
    case "very_active":
      return "非常活躍"
    case "active":
      return "活躍"
    case "inactive":
      return "不活躍"
    default:
      return "未知"
  }
}

// 獲取活躍等級的顏色樣式
export function getActivityLevelColor(level: ActivityLevel): string {
  switch (level) {
    case "very_active":
      return "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20"
    case "active":
      return "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
    case "inactive":
      return "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20"
    default:
      return "text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20"
  }
}

// 驗證活躍等級設定
export function validateActivityLevelSettings(settings: ActivityLevelSettings): boolean {
  return (
    settings.active >= 0 &&
    settings.active <= 100 &&
    settings.veryActive >= 0 &&
    settings.veryActive <= 100 &&
    settings.veryActive >= settings.active
  )
}
