import type { ActivityLevel, ActivityLevelSettings } from "../types"

// 預設活躍等級設定
export const DEFAULT_ACTIVITY_LEVEL_SETTINGS: ActivityLevelSettings = {
  veryActive: 80,
  active: 40,
}

// 根據出席率判斷活躍等級
export function getActivityLevel(attendanceRate: number, settings: ActivityLevelSettings): ActivityLevel {
  if (attendanceRate >= settings.veryActive) {
    return "very_active"
  } else if (attendanceRate >= settings.active) {
    return "active"
  } else {
    return "inactive"
  }
}

// 獲取活躍等級的顯示文字
export function getActivityLevelText(level: ActivityLevel): string {
  switch (level) {
    case "very_active":
      return "非常活躍"
    case "active":
      return "活躍"
    case "inactive":
      return "不活躍"
    default:
      return "未知"
  }
}

// 獲取活躍等級的顏色樣式
export function getActivityLevelColor(level: ActivityLevel): string {
  switch (level) {
    case "very_active":
      return "badge-success"
    case "active":
      return "badge-info"
    case "inactive":
      return "badge-error"
    default:
      return "badge-neutral"
  }
}

// 驗證活躍等級設定
export function validateActivityLevelSettings(settings: ActivityLevelSettings): boolean {
  return (
    settings.active >= 0 &&
    settings.active <= 100 &&
    settings.veryActive >= 0 &&
    settings.veryActive <= 100 &&
    settings.veryActive >= settings.active
  )
}
