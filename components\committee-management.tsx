"use client"

import { useState } from "react"
import type { Session } from "../types"
import { UniversalBulkImport } from "./universal-bulk-import"

interface CommitteeManagementProps {
  committees: string[]
  sessions?: Session[]
  selectedSessionId?: string | null
  onAddCommittee: (committee: string) => void
  onBulkAddCommittees: (committees: string[]) => void // 添加這行
  onEditCommittee: (oldName: string, newName: string) => void
  onDeleteCommittee: (committee: string) => void
  onBack: () => void
}

export function CommitteeManagement({
  committees,
  sessions = [],
  selectedSessionId,
  onAddCommittee,
  onBulkAddCommittees, // 添加這行
  onEditCommittee,
  onDeleteCommittee,
  onBack,
}: CommitteeManagementProps) {
  const [newCommittee, setNewCommittee] = useState("")
  const [editingCommittee, setEditingCommittee] = useState<{ old: string; new: string } | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [showBulkImport, setShowBulkImport] = useState(false) // 添加這行

  // 獲取當前屆別信息
  const currentSession = sessions.find((s) => s.id === selectedSessionId)

  // 處理新增委員會
  const handleAddCommittee = () => {
    if (newCommittee.trim() && !committees.includes(newCommittee.trim())) {
      onAddCommittee(newCommittee.trim())
      setNewCommittee("")
    }
  }

  // 處理編輯委員會
  const handleEditCommittee = () => {
    if (editingCommittee && editingCommittee.new.trim() && !committees.includes(editingCommittee.new.trim())) {
      onEditCommittee(editingCommittee.old, editingCommittee.new.trim())
      setEditingCommittee(null)
    }
  }

  // 處理批量導入委員會
  const handleBulkImport = (importedCommittees: string[]) => {
    // 過濾掉已存在的委員會
    const newCommittees = importedCommittees.filter((committee) => !committees.includes(committee))
    if (newCommittees.length > 0) {
      onBulkAddCommittees(newCommittees)
    }
    setShowBulkImport(false)
  }

  // 處理刪除委員會
  const handleDeleteCommittee = (committee: string) => {
    if (window.confirm(`確定要刪除委員會 "${committee}" 嗎？\n注意：這將影響所有相關的活動。`)) {
      onDeleteCommittee(committee)
    }
  }

  // 過濾委員會
  const filteredCommittees = committees.filter((committee) =>
    committee.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
        >
          ← 返回
        </button>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">委員會管理</h2>
        <button
          onClick={() => setShowBulkImport(true)} // 添加這行
          className="px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
            />
          </svg>
          批量導入
        </button>
      </div>

      {/* 批量導入對話框 */}
      {showBulkImport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <UniversalBulkImport
              dataType="committees"
              sessions={sessions}
              selectedSessionId={selectedSessionId}
              onImport={handleBulkImport}
              onCancel={() => setShowBulkImport(false)}
            />
          </div>
        </div>
      )}

      {/* 當前屆別信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">當前屆別</h3>
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
              {currentSession?.name || "未選擇屆別"}
            </span>
            {currentSession && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {currentSession.startDate} - {currentSession.endDate}
              </span>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {currentSession ? (
              <>
                屆別委員會: {currentSession.committees.length} 個
                <br />
                系統委員會: {committees.length} 個
              </>
            ) : (
              <>系統委員會: {committees.length} 個</>
            )}
          </div>
        </div>

        {/* 屆別委員會列表 */}
        {currentSession && currentSession.committees.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {currentSession.name} 的委員會：
            </h4>
            <div className="flex flex-wrap gap-2">
              {currentSession.committees.map((committee) => (
                <span
                  key={committee}
                  className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded flex items-center"
                >
                  {committee}
                  {committees.includes(committee) && (
                    <span className="ml-1 text-green-600 dark:text-green-400" title="已在系統中">
                      ✓
                    </span>
                  )}
                </span>
              ))}
            </div>
            {currentSession.committees.some((c) => !committees.includes(c)) && (
              <p className="mt-2 text-xs text-yellow-600 dark:text-yellow-400">注意：部分屆別委員會尚未在系統中創建</p>
            )}
          </div>
        )}
      </div>

      {/* 搜索 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">搜索委員會</label>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="輸入委員會名稱"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
        />
      </div>

      {/* 委員會列表 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            系統委員會列表 ({filteredCommittees.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-600">
          {filteredCommittees.length > 0 ? (
            filteredCommittees.map((committee, index) => {
              const isInCurrentSession = currentSession?.committees.includes(committee)
              return (
                <div
                  key={committee}
                  className="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-650"
                >
                  <div className="flex items-center">
                    <span className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 flex items-center justify-center text-sm font-medium mr-3">
                      {index + 1}
                    </span>
                    <div>
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{committee}</p>
                        {isInCurrentSession && (
                          <span className="ml-2 px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded">
                            當前屆別
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {isInCurrentSession ? "活躍委員會" : "系統委員會"}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingCommittee({ old: committee, new: committee })}
                      className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm"
                    >
                      編輯
                    </button>
                    <button
                      onClick={() => handleDeleteCommittee(committee)}
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 text-sm"
                      disabled={committee === "其他委員會"} // 防止刪除預設委員會
                    >
                      刪除
                    </button>
                  </div>
                </div>
              )
            })
          ) : (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">沒有找到符合條件的委員會</p>
            </div>
          )}
        </div>
      </div>

      {/* 新增委員會表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {editingCommittee ? "編輯委員會" : "新增委員會"}
        </h3>
        <div className="flex space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">委員會名稱</label>
            <input
              type="text"
              value={editingCommittee ? editingCommittee.new : newCommittee}
              onChange={(e) =>
                editingCommittee
                  ? setEditingCommittee({ ...editingCommittee, new: e.target.value })
                  : setNewCommittee(e.target.value)
              }
              placeholder="輸入委員會名稱"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex items-end space-x-2">
            {editingCommittee ? (
              <>
                <button
                  onClick={() => setEditingCommittee(null)}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleEditCommittee}
                  className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
                  disabled={!editingCommittee.new.trim() || committees.includes(editingCommittee.new.trim())}
                >
                  更新
                </button>
              </>
            ) : (
              <button
                onClick={handleAddCommittee}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                disabled={!newCommittee.trim() || committees.includes(newCommittee.trim())}
              >
                新增
              </button>
            )}
          </div>
        </div>
        {editingCommittee && committees.includes(editingCommittee.new.trim()) && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">委員會名稱已存在</p>
        )}
        {!editingCommittee && committees.includes(newCommittee.trim()) && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">委員會名稱已存在</p>
        )}

        {/* 快速添加當前屆別委員會 */}
        {currentSession && currentSession.committees.some((c) => !committees.includes(c)) && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">快速添加屆別委員會</h4>
            <div className="flex flex-wrap gap-2">
              {currentSession.committees
                .filter((c) => !committees.includes(c))
                .map((committee) => (
                  <button
                    key={committee}
                    onClick={() => onAddCommittee(committee)}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    + {committee}
                  </button>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* 統計信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">統計信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{committees.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">系統委員會總數</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {currentSession ? currentSession.committees.length : 0}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">當前屆別委員會</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {currentSession
                ? currentSession.committees.filter((c) => committees.includes(c)).length
                : committees.length}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">已創建委員會</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{filteredCommittees.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">搜索結果</p>
          </div>
        </div>
      </div>
    </div>
  )
}
