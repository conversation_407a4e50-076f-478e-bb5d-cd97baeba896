import type { Participant, SessionParticipant, Session, Activity } from "../../types"
import { UniversalBulkImport } from "../universal-bulk-import"
import { ParticipantAttendanceHistory } from "../participant-attendance-history"
import { TitleManagementSection } from "./title-management-section"

interface ParticipantManagementDialogsProps {
  // 對話框顯示狀態
  showTitleManagement: boolean
  showBulkImport: boolean
  viewingAttendanceHistory: Participant | null

  // 數據
  allCategories: string[]
  sessions: Session[]
  selectedSessionId: string | null
  activities: Activity[]
  sessionParticipants: SessionParticipant[]

  // 事件處理
  onUpdateTitle: (oldTitle: string, newTitle: string) => void
  onDeleteTitle: (title: string) => void
  onBulkImport: (data: any) => void
  onCloseTitleManagement: () => void
  onCloseBulkImport: () => void
  onCloseAttendanceHistory: () => void
}

/**
 * 參加者管理對話框組件
 * 用途：管理所有對話框的顯示和隱藏
 */
export function ParticipantManagementDialogs({
  showTitleManagement,
  showBulkImport,
  viewingAttendanceHistory,
  allCategories,
  sessions,
  selectedSessionId,
  activities,
  sessionParticipants,
  onUpdateTitle,
  onDeleteTitle,
  onBulkImport,
  onCloseTitleManagement,
  onCloseBulkImport,
  onCloseAttendanceHistory,
}: ParticipantManagementDialogsProps) {
  return (
    <>
      {/* 職銜管理對話框 */}
      {showTitleManagement && (
        <dialog className="modal modal-open">
          <div className="modal-box max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">職銜管理</h3>
              <button
                onClick={onCloseTitleManagement}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <TitleManagementSection
              allCategories={allCategories}
              onUpdateTitle={onUpdateTitle}
              onDeleteTitle={onDeleteTitle}
            />
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={onCloseTitleManagement}>close</button>
          </form>
        </dialog>
      )}

      {/* 批量導入對話框 */}
      {showBulkImport && (
        <dialog className="modal modal-open">
          <div className="modal-box max-w-4xl">
            <UniversalBulkImport
              dataType="participants"
              sessions={sessions}
              selectedSessionId={selectedSessionId}
              onImport={onBulkImport}
              onCancel={onCloseBulkImport}
            />
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={onCloseBulkImport}>close</button>
          </form>
        </dialog>
      )}

      {/* 出席記錄模態框 */}
      {viewingAttendanceHistory && (
        <ParticipantAttendanceHistory
          participant={viewingAttendanceHistory}
          activities={activities}
          sessionParticipants={sessionParticipants}
          sessions={sessions}
          onClose={onCloseAttendanceHistory}
        />
      )}
    </>
  )
}
