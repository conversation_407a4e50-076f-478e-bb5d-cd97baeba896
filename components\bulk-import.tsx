"use client"

import type React from "react"

import { useState } from "react"
import { parseFile } from "../utils/file-parsers"
import type { Participant } from "../types"

interface BulkImportProps {
  onImport: (participants: Omit<Participant, "id" | "attendance">[]) => void
  onCancel: () => void
}

export function BulkImport({ onImport, onCancel }: BulkImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<Omit<Participant, "id" | "attendance">[]>([])
  const [step, setStep] = useState<"upload" | "preview" | "complete">("upload")

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setError(null)
    }
  }

  const handleFileUpload = async () => {
    if (!file) {
      setError("請選擇一個文件")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const parsedData = await parseFile(file)

      if (parsedData.length === 0) {
        throw new Error("文件中未找到有效數據")
      }

      setPreview(parsedData)
      setStep("preview")
    } catch (err) {
      setError(err instanceof Error ? err.message : "文件解析失敗")
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmImport = () => {
    onImport(preview)
    setStep("complete")
  }

  const handleReset = () => {
    setFile(null)
    setPreview([])
    setError(null)
    setStep("upload")
  }

  if (step === "complete") {
    return (
      <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
        <div className="text-center mb-6">
          <div className="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-green-600 dark:text-green-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-4 text-xl font-medium text-gray-900 dark:text-white">導入成功</h3>
          <p className="mt-2 text-gray-600 dark:text-gray-300">成功導入 {preview.length} 位參加者</p>
        </div>
        <div className="flex justify-center">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">批量導入參加者</h3>

      {step === "upload" && (
        <>
          <div className="mb-4">
            <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
              上傳CSV或Excel文件以批量導入參加者。文件應包含以下列：
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 dark:text-gray-300">
              <li>姓名 (必填，列名可以是 "name" 或 "姓名" 或 "名稱")</li>
              <li>類別 (選填，列名可以是 "category" 或 "類別" 或 "類型" 或 "组別")</li>
            </ul>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">選擇文件</label>
            <input
              type="file"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
            />
            {file && <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">已選擇：{file.name}</p>}
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleFileUpload}
              disabled={!file || isLoading}
              className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "處理中..." : "上傳並預覽"}
            </button>
          </div>
        </>
      )}

      {step === "preview" && (
        <>
          <div className="mb-4">
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">數據預覽</h4>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
              共找到 {preview.length} 位參加者，請確認以下數據無誤：
            </p>
          </div>

          <div className="mb-6 max-h-60 overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    姓名
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    類別
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {preview.map((participant, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.name}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.category || "-"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={handleReset}
              className="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              返回
            </button>
            <button
              onClick={handleConfirmImport}
              className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            >
              確認導入
            </button>
          </div>
        </>
      )}
    </div>
  )
}
