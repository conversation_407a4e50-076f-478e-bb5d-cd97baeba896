import type { Participant, SessionParticipant } from "../types"

interface UseParticipantManagementHandlersProps {
  // Props
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  selectedSessionId: string | null
  onAddParticipant: (participant: Omit<Participant, "id" | "attendance">) => void
  onBulkAddSessionParticipants: (sessionParticipants: Omit<SessionParticipant, "id">[]) => void
  onBulkAddParticipants: (participants: Omit<Participant, "id" | "attendance">[]) => void
  onUpdateParticipant: (participant: Participant) => void
  onUpdateSessionParticipant: (sessionParticipant: SessionParticipant) => void
  onBulkDeleteTitle: (title: string) => { participantsUpdated: number; placeholdersRemoved: number; sessionParticipantsUpdated: number }

  // State setters
  setNewParticipant: (participant: { name: string; category: string }) => void
  setNewParticipantTitle: (title: string) => void
  setIsAddingParticipant: (adding: boolean) => void
  setEditingParticipant: (participant: Participant | null) => void
  setEditingSessionParticipant: (participant: SessionParticipant | null) => void
  setShowBulkImport: (show: boolean) => void
  setShowAddExisting: (show: boolean) => void
  setSortField: (field: string) => void
  setSortDirection: (direction: "asc" | "desc") => void

  // State values
  newParticipant: { name: string; category: string }
  newParticipantTitle: string
  isAddingParticipant: boolean
  editingParticipant: Participant | null
  editingSessionParticipant: SessionParticipant | null
  sortField: string
  sortDirection: "asc" | "desc"
  allCategories: string[]
}

/**
 * 參加者管理事件處理 Hook
 * 用途：集中管理參加者管理頁面的所有事件處理邏輯
 */
export function useParticipantManagementHandlers({
  allParticipants,
  sessionParticipants,
  selectedSessionId,
  onAddParticipant,
  onBulkAddSessionParticipants,
  onBulkAddParticipants,
  onUpdateParticipant,
  onUpdateSessionParticipant,
  onBulkDeleteTitle,
  setNewParticipant,
  setNewParticipantTitle,
  setIsAddingParticipant,
  setEditingParticipant,
  setEditingSessionParticipant,
  setShowBulkImport,
  setShowAddExisting,
  setSortField,
  setSortDirection,
  newParticipant,
  newParticipantTitle,
  isAddingParticipant,
  editingParticipant,
  editingSessionParticipant,
  sortField,
  sortDirection,
  allCategories,
}: UseParticipantManagementHandlersProps) {

  // 排序處理函數
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // 處理批量導入
  const handleBulkImport = (data: any) => {
    if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object' && 'name' in data[0]) {
      onBulkAddParticipants(data as Omit<Participant, "id" | "attendance">[])
    }
    setShowBulkImport(false)
  }

  // 處理批量添加到屆別
  const handleBatchAddToSession = (selectedParticipants: Set<string>) => {
    if (selectedSessionId && selectedParticipants.size > 0) {
      const sessionParticipantsToAdd: Omit<SessionParticipant, "id">[] = []
      const skippedParticipants: string[] = []

      selectedParticipants.forEach((participantId) => {
        const participant = allParticipants.find((p) => p.id === participantId)
        if (participant) {
          // 檢查該參加者是否已經在當前屆別中
          const existingSessionParticipant = sessionParticipants.find(
            sp => sp.participantId === participant.id && sp.sessionId === selectedSessionId
          )

          if (existingSessionParticipant) {
            skippedParticipants.push(participant.name)
          } else {
            sessionParticipantsToAdd.push({
              participantId: participant.id,
              sessionId: selectedSessionId,
              name: participant.name,
              category: participant.category || "",
              joinDate: new Date().toISOString().split("T")[0],
              isActive: true,
            })
          }
        }
      })

      // 使用批量添加函數
      if (sessionParticipantsToAdd.length > 0) {
        onBulkAddSessionParticipants(sessionParticipantsToAdd)

        let message = `成功添加 ${sessionParticipantsToAdd.length} 位成員到當前屆別`
        if (skippedParticipants.length > 0) {
          message += `\n\n跳過已存在的成員：${skippedParticipants.join(', ')}`
        }
        alert(message)
      } else if (skippedParticipants.length > 0) {
        alert(`所有選中的成員都已經在當前屆別中：${skippedParticipants.join(', ')}`)
      }

      setShowAddExisting(false)
    }
  }

  // 處理新增參加者
  const handleAddParticipant = async () => {
    if (newParticipant.name.trim() && !isAddingParticipant) {
      setIsAddingParticipant(true)
      try {
        const trimmedName = newParticipant.name.trim()

        // 檢查是否已存在相同姓名的參加者
        const existingParticipant = allParticipants.find(
          p => p.name.toLowerCase() === trimmedName.toLowerCase()
        )

        if (existingParticipant) {
          alert(`參加者「${trimmedName}」已存在，請使用不同的姓名`)
          return
        }

        // 處理新增職銜的情況
        let finalCategory = newParticipant.category
        if (finalCategory === "新增職銜") {
          finalCategory = newParticipantTitle.trim()
        }

        const participantToAdd = {
          name: trimmedName,
          category: finalCategory
        }

        onAddParticipant(participantToAdd)
        setNewParticipant({ name: "", category: "" })
        setNewParticipantTitle("")

        alert(`成功新增參加者「${participantToAdd.name}」${finalCategory ? `，職銜：${finalCategory}` : ''}`)
      } catch (error) {
        console.error('新增參加者時發生錯誤:', error)
        alert(`新增參加者時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
      } finally {
        setIsAddingParticipant(false)
      }
    }
  }

  // 處理更新參加者
  const handleUpdateParticipant = () => {
    if (editingParticipant) {
      onUpdateParticipant(editingParticipant)
      setEditingParticipant(null)
    }
  }

  // 處理更新屆別參加者
  const handleUpdateSessionParticipant = () => {
    if (editingSessionParticipant) {
      onUpdateSessionParticipant(editingSessionParticipant)
      setEditingSessionParticipant(null)
    }
  }

  // 處理職銜編輯
  const handleUpdateTitle = (oldTitle: string, newTitle: string) => {
    if (newTitle.trim() && newTitle !== oldTitle) {
      try {
        // 檢查新職銜是否已存在（不區分大小寫）
        if (allCategories.some(category =>
          category.toLowerCase() === newTitle.trim().toLowerCase() &&
          category.toLowerCase() !== oldTitle.toLowerCase()
        )) {
          alert(`職銜「${newTitle.trim()}」已存在`)
          return
        }

        // 找出所有使用該職銜的參加者
        const participantsToUpdate = allParticipants.filter(p =>
          p.category === oldTitle &&
          !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者
        )

        // 找出佔位符參加者（如果有）
        const placeholders = allParticipants.filter(p =>
          p.category === oldTitle &&
          p.name.startsWith('職銜佔位符-')
        )

        // 找出所有使用該職銜的屆別參加者
        const sessionParticipantsToUpdate = sessionParticipants.filter(sp =>
          sp.category === oldTitle
        )

        // 顯示進度提示
        const totalUpdates = participantsToUpdate.length
        const hasPlaceholders = placeholders.length > 0

        // 確認是否繼續
        if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {
          if (!window.confirm(`將更新職銜從「${oldTitle}」到「${newTitle}」，${totalUpdates > 0 ? `影響 ${totalUpdates} 位參加者` : ''}。\n\n確定繼續嗎？`)) {
            return
          }

          // 批量更新全局參加者
          if (participantsToUpdate.length > 0) {
            participantsToUpdate.forEach(participant => {
              const updatedParticipant = { ...participant, category: newTitle }
              onUpdateParticipant(updatedParticipant)
            })
          }

          // 批量更新屆別參加者
          if (sessionParticipantsToUpdate.length > 0) {
            sessionParticipantsToUpdate.forEach(sessionParticipant => {
              if ('category' in sessionParticipant) {
                const updatedSessionParticipant = { ...sessionParticipant, category: newTitle }
                onUpdateSessionParticipant(updatedSessionParticipant)
              }
            })
          }

          // 顯示完成提示
          if (totalUpdates > 0) {
            alert(`成功更新 ${totalUpdates} 位參加者的職銜從「${oldTitle}」到「${newTitle}」`)
          } else if (hasPlaceholders) {
            alert(`成功更新職銜「${oldTitle}」為「${newTitle}」`)
          } else {
            alert(`成功更新職銜「${oldTitle}」為「${newTitle}」，沒有參加者使用此職銜`)
          }
        } else {
          alert(`沒有找到使用「${oldTitle}」職銜的參加者`)
        }
      } catch (error) {
        console.error('更新職銜時發生錯誤:', error)
        alert(`更新職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
      }
    }
  }

  // 處理職銜刪除
  const handleDeleteTitle = (title: string) => {
    try {
      // 找出所有使用該職銜的實際參加者（排除佔位符）
      const participantsToUpdate = allParticipants.filter(p =>
        p.category === title &&
        !p.name.startsWith('職銜佔位符-')
      )

      // 找出使用該職銜的佔位符參加者
      const placeholders = allParticipants.filter(p =>
        p.category === title &&
        p.name.startsWith('職銜佔位符-')
      )

      const totalUpdates = participantsToUpdate.length
      const hasPlaceholders = placeholders.length > 0

      let confirmMessage = `確定要刪除職銜 "${title}" 嗎？`

      if (totalUpdates > 0) {
        confirmMessage += `\n\n這將會：`
        confirmMessage += `\n• 清除 ${totalUpdates} 位成員的職銜`
        confirmMessage += `\n• 將他們的職銜設為空白`
      }

      if (hasPlaceholders) {
        confirmMessage += totalUpdates > 0 ? `\n• 移除職銜佔位符` : `\n\n這將移除職銜佔位符。`
      }

      if (totalUpdates === 0 && !hasPlaceholders) {
        confirmMessage += `\n\n沒有成員使用此職銜，將直接移除。`
      }

      if (window.confirm(confirmMessage)) {
        try {
          // 使用新的批量刪除函數
          const result = onBulkDeleteTitle(title)

          // 顯示完成提示
          let successMessage = `成功刪除職銜 "${title}"`

          if (result.participantsUpdated > 0) {
            successMessage += `\n\n已完成：`
            successMessage += `\n• 清除了 ${result.participantsUpdated} 位成員的職銜`
            successMessage += `\n• 這些成員的職銜現在為空白`
          }

          if (result.placeholdersRemoved > 0) {
            successMessage += result.participantsUpdated > 0 ? `\n• 移除了職銜佔位符` : `\n\n已移除職銜佔位符。`
          }

          if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {
            successMessage += `\n\n沒有成員使用此職銜，已直接移除。`
          }

          alert(successMessage)
        } catch (error) {
          console.error('刪除職銜時發生錯誤:', error)
          alert(`刪除職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
        }
      }
    } catch (error) {
      console.error('刪除職銜時發生錯誤:', error)
      alert(`刪除職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
    }
  }

  return {
    handleSort,
    handleBulkImport,
    handleBatchAddToSession,
    handleAddParticipant,
    handleUpdateParticipant,
    handleUpdateSessionParticipant,
    handleUpdateTitle,
    handleDeleteTitle,
  }
}
