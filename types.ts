// 修改報名狀態枚舉，移除無意義的狀態
export enum RegistrationStatus {
  REGISTERED_ONLY = "registered_only", // 僅報名（活動還未開始或未記錄出席）
  ATTENDED = "attended", // 已出席（可能有報名也可能沒有）
  NO_SHOW = "no_show", // 爽約（報名但未出席）
}

// 新增：活躍等級枚舉
export enum ActivityLevel {
  VERY_ACTIVE = "very_active",
  ACTIVE = "active",
  INACTIVE = "inactive",
}

// 活躍等級設定
export interface ActivityLevelSettings {
  veryActive: number // 非常活躍閾值（百分比）
  active: number // 活躍閾值（百分比）
}

// 出席狀態類型
export interface AttendanceStatus {
  status: 'present' | 'absent' | 'late' | 'excused'
  checkInTime?: string
  notes?: string
  recordedBy?: string
}

// 參加者類型
export interface Participant {
  id: string
  name: string
  category: string
  attendance: Record<string, boolean | AttendanceStatus> // 活動ID或日期 -> 出席狀態
  registration?: Record<string, boolean> // 報名記錄
}

// 活動類型
export interface Activity {
  id: string
  sessionId?: string
  name: string
  date: string
  location?: string
  description?: string
  committee: string
  type?: string
  maxParticipants?: number
  requiresRegistration?: boolean
  registrationDeadline?: string
  participants: Participant[]
}

// 屆別類型
export interface Session {
  id: string
  name: string
  startDate: string
  endDate: string
  description?: string
  isActive: boolean
  committees: string[]
}

// 屆別參加者關聯類型
export interface SessionParticipant {
  id: string
  participantId: string
  sessionId: string
  name: string
  category: string
  joinDate: string
  isActive: boolean
}

// 出席記錄類型（用於統計和報告）
export interface AttendanceRecord {
  participantId: string
  participantName?: string
  activityId: string
  activityName?: string
  status: 'present' | 'absent' | 'late' | 'excused'
  checkInTime?: string
  notes?: string
  recordedBy?: string
}
