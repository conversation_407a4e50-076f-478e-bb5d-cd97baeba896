"use client"

import { useMemo, useState } from "react"
import { cn } from "@/lib/utils"
import type { Activity, Participant, Session } from "../types"
import { calculateStatistics } from "../utils/statistics"
import { ExportDialog } from "./export-dialog"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"

interface StatisticsDashboardProps {
  activities: Activity[]
  allParticipants: Participant[]
  sessions?: Session[] // 添加這行
  onBack: () => void
}

export function StatisticsDashboard({
  activities = [],
  allParticipants = [],
  sessions = [],
  onBack,
}: StatisticsDashboardProps) {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(
    sessions.find((s) => s.isActive)?.id || sessions[0]?.id || null,
  )
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: "",
    useCustomRange: false,
  })

  const [topRankingDisplayCount, setTopRankingDisplayCount] = useState(5)
  const [committeeStatsDisplayCount, setCommitteeStatsDisplayCount] = useState(5)

  // 根據屆別過濾活動
  const sessionFilteredActivities = useMemo(() => {
    if (!selectedSessionId) return activities
    return activities.filter((activity) => activity.sessionId === selectedSessionId)
  }, [activities, selectedSessionId])

  // 獲取活動的日期範圍 - 基於屆別篩選後的活動
  const activityDateRange = useMemo(() => {
    if (sessionFilteredActivities.length === 0) return { min: "", max: "" }

    const dates = sessionFilteredActivities.map((a) => a.date).sort()
    return {
      min: dates[0],
      max: dates[dates.length - 1],
    }
  }, [sessionFilteredActivities])

  // 根據日期範圍過濾活動 - 基於屆別篩選後的活動
  const filteredActivities = useMemo(() => {
    if (!dateRange.useCustomRange || (!dateRange.startDate && !dateRange.endDate)) {
      return sessionFilteredActivities
    }

    return sessionFilteredActivities.filter((activity) => {
      const activityDate = activity.date
      const isAfterStart = !dateRange.startDate || activityDate >= dateRange.startDate
      const isBeforeEnd = !dateRange.endDate || activityDate <= dateRange.endDate
      return isAfterStart && isBeforeEnd
    })
  }, [sessionFilteredActivities, dateRange])

  // 計算統計數據
  const statistics = useMemo(
    () => calculateStatistics(filteredActivities, allParticipants, sessions),
    [filteredActivities, allParticipants, sessions],
  )

  // 準備委員會統計數據
  const committeeStats = useMemo(() => {
    const stats: Record<string, { total: number; attended: number; activities: number }> = {}

    filteredActivities.forEach((activity) => {
      if (!stats[activity.committee]) {
        stats[activity.committee] = { total: 0, attended: 0, activities: 0 }
      }

      stats[activity.committee].activities++
      activity.participants.forEach((participant) => {
        stats[activity.committee].total++
        if (participant.attendance[activity.date]) {
          stats[activity.committee].attended++
        }
      })
    })

    return Object.entries(stats).map(([committee, data]) => ({
      name: committee,
      出席率: data.total > 0 ? (data.attended / data.total) * 100 : 0,
      活動數: data.activities,
      總參與人次: data.total,
      出席人次: data.attended,
    }))
  }, [filteredActivities])

  // 準備參加者類別統計 - 基於屆別篩選
  const categoryStats = useMemo(() => {
    // 獲取當前篩選條件下實際參與活動的參加者ID
    const activeParticipantIds = new Set<string>()
    sessionFilteredActivities.forEach((activity) => {
      activity.participants.forEach((participant) => {
        activeParticipantIds.add(participant.id)
      })
    })

    // 只統計實際參與篩選後活動的參加者
    const activeParticipants = allParticipants.filter((p) => activeParticipantIds.has(p.id))

    const categoryCount: Record<string, number> = {}
    activeParticipants.forEach((participant) => {
      const category = participant.category || "未分類"
      categoryCount[category] = (categoryCount[category] || 0) + 1
    })

    return Object.entries(categoryCount).map(([category, count]) => ({
      name: category,
      value: count,
    }))
  }, [sessionFilteredActivities, allParticipants])

  // 準備月度趨勢數據
  const monthlyTrends = useMemo(() => {
    const monthlyData: Record<string, { attended: number; total: number }> = {}

    filteredActivities.forEach((activity) => {
      const monthKey = activity.date.substring(0, 7) // YYYY-MM
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { attended: 0, total: 0 }
      }

      activity.participants.forEach((participant) => {
        monthlyData[monthKey].total++
        if (participant.attendance[activity.date]) {
          monthlyData[monthKey].attended++
        }
      })
    })

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        月份: month,
        出席率: data.total > 0 ? (data.attended / data.total) * 100 : 0,
        總參與人次: data.total,
        出席人次: data.attended,
      }))
      .sort((a, b) => a.月份.localeCompare(b.月份))
  }, [filteredActivities])

  // 準備頂級參加者數據
  const topParticipants = useMemo(() => {
    return statistics.participantStats
      .filter((p) => p.totalActivities > 0)
      .sort((a, b) => b.attendanceRate - a.attendanceRate)
      .slice(0, 10)
      .map((p) => ({
        姓名: p.name,
        出席率: p.attendanceRate,
        參與活動數: p.totalActivities,
        出席次數: p.attendedActivities,
      }))
  }, [statistics.participantStats])

  // 快速日期範圍設置
  const setQuickDateRange = (type: "week" | "month" | "quarter" | "year" | "all") => {
    const now = new Date()
    const today = now.toISOString().split("T")[0]

    switch (type) {
      case "week":
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        setDateRange({
          startDate: weekAgo.toISOString().split("T")[0],
          endDate: today,
          useCustomRange: true,
        })
        break
      case "month":
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        setDateRange({
          startDate: monthAgo.toISOString().split("T")[0],
          endDate: today,
          useCustomRange: true,
        })
        break
      case "quarter":
        const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
        setDateRange({
          startDate: quarterAgo.toISOString().split("T")[0],
          endDate: today,
          useCustomRange: true,
        })
        break
      case "year":
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        setDateRange({
          startDate: yearAgo.toISOString().split("T")[0],
          endDate: today,
          useCustomRange: true,
        })
        break
      case "all":
        setDateRange({
          startDate: "",
          endDate: "",
          useCustomRange: false,
        })
        break
    }
  }

  // 重置日期範圍
  const resetDateRange = () => {
    setDateRange({
      startDate: "",
      endDate: "",
      useCustomRange: false,
    })
  }

  // 圖表顏色
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d", "#ffc658", "#ff7300"]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
        >
          ← 返回
        </button>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">詳細統計分析</h2>
        <button
          onClick={() => setShowExportDialog(true)}
          className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          導出統計
        </button>
      </div>

      {/* 屆別選擇器 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">屆別選擇</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">選擇屆別</label>
            <select
              value={selectedSessionId || ""}
              onChange={(e) => {
                setSelectedSessionId(e.target.value || null)
                // 重置日期範圍當切換屆別時
                setDateRange({
                  startDate: "",
                  endDate: "",
                  useCustomRange: false,
                })
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            >
              <option value="">全部屆別</option>
              {sessions
                .sort((a, b) => b.name.localeCompare(a.name)) // 按屆別名稱降序排列
                .map((session) => (
                  <option key={session.id} value={session.id}>
                    {session.name} ({session.startDate} - {session.endDate}){session.isActive ? " (當前)" : ""}
                  </option>
                ))}
            </select>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {selectedSessionId ? (
              <>
                當前屆別: <span className="font-medium">{sessions.find((s) => s.id === selectedSessionId)?.name}</span>
                <br />
                活動數量: <span className="font-medium">{sessionFilteredActivities.length}</span>
              </>
            ) : (
              <>
                顯示全部屆別
                <br />
                總活動數量: <span className="font-medium">{activities.length}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 自定義時間範圍選擇器 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">時間範圍</h3>

        {/* 快速選擇按鈕 */}
        <div className="flex flex-wrap gap-2 mb-4">
          {[
            { key: "week", label: "最近一週" },
            { key: "month", label: "最近一個月" },
            { key: "quarter", label: "最近三個月" },
            { key: "year", label: "最近一年" },
            { key: "all", label: "全部時間" },
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setQuickDateRange(key as any)}
              className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
            >
              {label}
            </button>
          ))}
        </div>

        {/* 自定義日期範圍 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">開始日期</label>
            <input
              type="date"
              value={dateRange.startDate}
              min={activityDateRange.min}
              max={activityDateRange.max}
              onChange={(e) =>
                setDateRange((prev) => ({
                  ...prev,
                  startDate: e.target.value,
                  useCustomRange: true,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">結束日期</label>
            <input
              type="date"
              value={dateRange.endDate}
              min={dateRange.startDate || activityDateRange.min}
              max={activityDateRange.max}
              onChange={(e) =>
                setDateRange((prev) => ({
                  ...prev,
                  endDate: e.target.value,
                  useCustomRange: true,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>
          <div className="flex space-x-2">
            <button
              onClick={resetDateRange}
              className="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
            >
              重置
            </button>
            <button
              onClick={() => setDateRange((prev) => ({ ...prev, useCustomRange: true }))}
              disabled={!dateRange.startDate && !dateRange.endDate}
              className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              應用
            </button>
          </div>
        </div>

        {/* 當前篩選狀態 */}
        <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex flex-wrap gap-4">
            <span>屆別: {selectedSessionId ? sessions.find((s) => s.id === selectedSessionId)?.name : "全部屆別"}</span>
            <span>
              {dateRange.useCustomRange ? (
                <>
                  時間範圍: {dateRange.startDate || "開始"} 至 {dateRange.endDate || "結束"}
                </>
              ) : (
                "時間範圍: 全部時間"
              )}
            </span>
            <span>結果: {filteredActivities.length} 個活動</span>
          </div>
          {activityDateRange.min && activityDateRange.max && (
            <div className="mt-1 text-xs">
              可用時間範圍: {activityDateRange.min} 至 {activityDateRange.max}
            </div>
          )}
        </div>
      </div>

      {/* 關鍵指標 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">總活動數</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{filteredActivities.length}</p>
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {selectedSessionId && `屆別篩選: ${sessionFilteredActivities.length} / ${activities.length}`}
            {dateRange.useCustomRange &&
              ` | 日期篩選: ${filteredActivities.length} / ${sessionFilteredActivities.length}`}
          </div>
        </div>
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">總參加者數</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{allParticipants.length}</p>
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            活躍參加者: {statistics.participantStats.filter((p) => p.totalActivities > 0).length} 人
          </div>
        </div>
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">平均出席率</h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
            {statistics.averageAttendanceRate.toFixed(1)}%
          </p>
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {statistics.averageAttendanceRate >= 80 ? (
              <span className="text-green-600 dark:text-green-400">優秀</span>
            ) : statistics.averageAttendanceRate >= 60 ? (
              <span className="text-yellow-600 dark:text-yellow-400">良好</span>
            ) : (
              <span className="text-red-600 dark:text-red-400">需改進</span>
            )}
          </div>
        </div>
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">總出席人次</h3>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{statistics.totalAttendanceRecords}</p>
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            總可能出席: {statistics.totalPossibleAttendance}
          </div>
        </div>
      </div>

      {/* 參與度統計 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">參與度統計</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300">整體平均參與度</h4>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {statistics.participationStats.averageParticipationRate.toFixed(1)}%
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              成員參與活動的平均比例
            </p>
          </div>
          
          {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-700 dark:text-green-300">當前屆別參與度</h4>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1)}%
              </p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName}
              </p>
            </div>
          )}
          
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">活躍成員數</h4>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId]
                ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants
                : statistics.participantStats.filter(p => p.participationRate > 0).length}
            </p>
            <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
              參與過活動的成員
            </p>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-orange-700 dark:text-orange-300">高參與度成員</h4>
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {statistics.participationStats.highParticipationStats.count}
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              ≥{statistics.participationStats.highParticipationStats.threshold}% ({statistics.participationStats.highParticipationStats.percentage.toFixed(1)}%)
            </p>
          </div>
        </div>
        
        {/* 各屆別參與度對比 */}
        {Object.keys(statistics.participationStats.sessionParticipationStats).length > 1 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">各屆別參與度對比</h4>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={Object.entries(statistics.participationStats.sessionParticipationStats).map(([id, stats]) => ({
                  name: stats.sessionName,
                  參與度: stats.averageParticipationRate,
                  活躍成員: stats.activeParticipants,
                  總活動數: stats.totalActivities,
                }))}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="參與度" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>

      {/* 圖表區域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 委員會出席率 */}
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">委員會出席率</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={committeeStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="出席率" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 參加者類別分布 */}
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">參加者類別分布</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={categoryStats}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {categoryStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 月度出席趨勢 */}
        {monthlyTrends.length > 1 && (
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 lg:col-span-2">
            <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">月度出席趨勢</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={monthlyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="月份" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="出席率" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>

      {/* 詳細統計表格 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 頂級參加者 */}
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">出席率排行榜</h3>
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600 dark:text-gray-400">顯示數量:</label>
              <input
                type="number"
                min="1"
                max="20"
                value={topRankingDisplayCount}
                onChange={(e) =>
                  setTopRankingDisplayCount(Math.max(1, Math.min(20, Number.parseInt(e.target.value) || 5)))
                }
                className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">筆</span>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    排名
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    姓名
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    出席率
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {topParticipants.slice(0, topRankingDisplayCount).map((participant, index) => (
                  <tr key={participant.姓名}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">#{index + 1}</td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.姓名}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-right font-medium">
                      <span
                        className={cn(
                          participant.出席率 >= 80
                            ? "text-green-600 dark:text-green-400"
                            : participant.出席率 >= 60
                              ? "text-yellow-600 dark:text-yellow-400"
                              : "text-red-600 dark:text-red-400",
                        )}
                      >
                        {participant.出席率.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 委員會詳細統計 */}
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">委員會詳細統計</h3>
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600 dark:text-gray-400">顯示數量:</label>
              <input
                type="number"
                min="1"
                max="20"
                value={committeeStatsDisplayCount}
                onChange={(e) =>
                  setCommitteeStatsDisplayCount(Math.max(1, Math.min(20, Number.parseInt(e.target.value) || 5)))
                }
                className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">筆</span>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    委員會
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    活動數
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    出席率
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {committeeStats.slice(0, committeeStatsDisplayCount).map((committee) => (
                  <tr key={committee.name}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {committee.name}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
                      {committee.活動數}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-right font-medium">
                      <span
                        className={cn(
                          committee.出席率 >= 80
                            ? "text-green-600 dark:text-green-400"
                            : committee.出席率 >= 60
                              ? "text-yellow-600 dark:text-yellow-400"
                              : "text-red-600 dark:text-red-400",
                        )}
                      >
                        {committee.出席率.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 導出對話框 */}
      {showExportDialog && (
        <ExportDialog
          activities={filteredActivities}
          allParticipants={allParticipants}
          onClose={() => setShowExportDialog(false)}
        />
      )}
    </div>
  )
}
