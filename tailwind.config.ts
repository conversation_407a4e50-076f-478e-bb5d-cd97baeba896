import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},

  	}
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        "professional": {
          "primary": "#1e40af",           // Deep professional blue
          "primary-content": "#ffffff",
          "secondary": "#475569",         // Sophisticated slate
          "secondary-content": "#ffffff",
          "accent": "#7c3aed",           // Elegant purple
          "accent-content": "#ffffff",
          "neutral": "#334155",          // Refined dark gray
          "neutral-content": "#ffffff",
          "base-100": "#ffffff",         // Pure white background
          "base-200": "#f8fafc",         // Subtle off-white
          "base-300": "#e2e8f0",         // Light gray for borders
          "base-content": "#0f172a",     // Rich dark text
          "info": "#0ea5e9",             // Professional cyan
          "info-content": "#ffffff",
          "success": "#059669",          // Trustworthy green
          "success-content": "#ffffff",
          "warning": "#d97706",          // Warm amber
          "warning-content": "#ffffff",
          "error": "#dc2626",            // Clear red
          "error-content": "#ffffff",
        },
        "professional-dark": {
          "primary": "#3b82f6",          // Bright blue for dark mode
          "primary-content": "#ffffff",
          "secondary": "#64748b",        // Lighter slate for contrast
          "secondary-content": "#ffffff",
          "accent": "#8b5cf6",           // Vibrant purple
          "accent-content": "#ffffff",
          "neutral": "#475569",          // Medium gray
          "neutral-content": "#ffffff",
          "base-100": "#0f172a",         // Deep navy background
          "base-200": "#1e293b",         // Elevated surface
          "base-300": "#334155",         // Border color
          "base-content": "#f1f5f9",     // Light text
          "info": "#0ea5e9",             // Bright cyan
          "info-content": "#ffffff",
          "success": "#10b981",          // Vibrant green
          "success-content": "#ffffff",
          "warning": "#f59e0b",          // Bright amber
          "warning-content": "#ffffff",
          "error": "#ef4444",            // Bright red
          "error-content": "#ffffff",
        },
      },
    ],
    darkTheme: "professional-dark",
    base: true,
    styled: true,
    utils: true,
    prefix: "",
    logs: true,
    themeRoot: ":root",
  },
};
export default config;
